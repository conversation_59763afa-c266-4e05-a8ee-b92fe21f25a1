<template>
  <div class="">
    <!-- 加载状态 -->
    <div v-if="iconsLoading" class="flex-c py-40px">
      <div class="flex items-center gap-8px text-gray-500">
        <i class="i-line-md-loading-twotone-loop text-16px"></i>
        <span class="text-sm">{{ $t('iconSelector.loadingIcons') }}</span>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="iconsLoadError" class="flex-c py-40px">
      <div class="text-center">
        <div class="mb-8px text-sm text-red-500">{{ iconsLoadError }}</div>
        <button
          class="text-sm text-blue-500 hover:text-blue-600"
          @click="fetchLucideIcons"
        >
          {{ $t('iconSelector.retryLoadIcons') }}
        </button>
      </div>
    </div>

    <!-- 正常状态 -->
    <div v-else>
      <!-- 搜索框 -->
      <div class="mb-15px">
        <input
          v-model="iconSearchQuery"
          type="text"
          class="w-full border border-gray-300 rounded-md px-12px py-8px text-sm focus:border-blue-500 focus:outline-none"
          :placeholder="$t('iconSelector.iconSearchPlaceholder')"
        >
      </div>

      <!-- 图标网格 -->
      <div v-if="filteredIcons.length > 0" class="icon-selector">
        <div class="grid grid-cols-8 gap-8px">
          <div
            v-for="icon in filteredIcons"
            :key="icon"
            class="size-40px flex-c cursor-pointer border border-gray-200 rounded-md transition-colors hover:border-blue-400 hover:bg-blue-50"
            :class="{
              'border-blue-500 bg-blue-100': selectedIcon === icon,
            }"
            :title="icon"
            @click="selectIcon(icon)"
          >
            <img
              :src="`https://api.iconify.design/lucide/${icon}.svg?color=%23374151&width=20&height=20`"
              :alt="icon"
              class="size-20px"
              loading="lazy"
            >
          </div>
        </div>
        <!-- 加载更多 -->
        <div v-if="!allIconsLoaded && filteredIcons.length > 0" class="mt-15px text-center">
          <button
            class="text-sm text-blue-500 hover:text-blue-600"
            @click="loadMoreIcons"
          >
            {{ $t('iconSelector.loadMoreIcons') }}
          </button>
          <!-- 图标总数显示 -->
          <div v-if="lucideIcons.length > 0" class="mt-10px text-center text-xs text-gray-400">
            {{ $t('iconSelector.totalIcons', { count: lucideIcons.length, left: lucideIcons.length - displayedIconsCount }) }}
          </div>
        </div>
      </div>

      <!-- 无搜索结果 -->
      <div v-else-if="iconSearchQuery.trim()" class="flex-c py-40px">
        <div class="text-center text-gray-500">
          <div class="text-sm">{{ $t('iconSelector.noIconsFound') }}</div>
          <div class="mt-4px text-xs">{{ $t('iconSelector.tryDifferentKeywords') }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  visible?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
})

const emit = defineEmits<Emits>()

interface Emits {
  (e: 'select', iconName: string): void
}

const selectedIcon = ref('')
const iconSearchQuery = ref('')
const displayedIconsCount = ref(80) // 初始显示的图标数量
const allIconsLoaded = ref(false)

// 动态获取的 Lucide 图标列表
const lucideIcons = ref<string[]>([])
const iconsLoading = ref(false)
const iconsLoadError = ref('')

// 获取 Lucide 图标列表
async function fetchLucideIcons() {
  if (lucideIcons.value.length > 0) { return } // 已经加载过了

  iconsLoading.value = true
  iconsLoadError.value = ''

  try {
    const response = await fetch('https://api.iconify.design/collection?prefix=lucide')
    if (response.ok) {
      const data = await response.json()
      // 获取所有可见的图标
      // API 返回格式：{ uncategorized: [...], hidden: [...], aliases: {...} }
      lucideIcons.value = data.uncategorized || []
    }
    else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
  }
  catch (error) {
    console.error('Failed to fetch Lucide icons:', error)
    iconsLoadError.value = '加载图标列表失败，请检查网络连接'
    // 提供一些基础图标作为后备
    lucideIcons.value = [
      'house',
      'user',
      'settings',
      'search',
      'heart',
      'star',
      'bookmark',
      'bell',
      'mail',
      'phone',
      'camera',
      'image',
      'video',
      'music',
      'file',
      'folder',
      'download',
      'upload',
      'share',
      'link',
      'copy',
      'square-pen',
      'trash',
      'plus',
      'minus',
      'check',
      'x',
      'arrow-left',
      'arrow-right',
      'arrow-up',
      'arrow-down',
    ]
  }
  finally {
    iconsLoading.value = false
  }
}

// 过滤后的图标列表（不包含数量限制）
const filteredIconsList = computed(() => {
  let icons = lucideIcons.value

  // 根据搜索查询过滤
  if (iconSearchQuery.value.trim()) {
    const query = iconSearchQuery.value.toLowerCase().trim()
    icons = icons.filter((icon: string) => icon.includes(query))
  }

  return icons
})

// 显示的图标列表（包含数量限制）
const filteredIcons = computed(() => {
  return filteredIconsList.value.slice(0, displayedIconsCount.value)
})

// 监听状态变化，更新 allIconsLoaded
watchEffect(() => {
  allIconsLoaded.value = displayedIconsCount.value >= filteredIconsList.value.length
})

// 选择图标
function selectIcon(iconName: string) {
  selectedIcon.value = iconName
  emit('select', iconName)
}

// 加载更多图标
function loadMoreIcons() {
  const increment = 80
  const newCount = displayedIconsCount.value + increment
  const totalIcons = filteredIconsList.value.length

  if (newCount >= totalIcons) {
    displayedIconsCount.value = totalIcons
  }
  else {
    displayedIconsCount.value = newCount
  }
}

// 重置状态
function resetState() {
  selectedIcon.value = ''
  iconSearchQuery.value = ''
  displayedIconsCount.value = 80
}

// 监听 visible 变化，当组件显示时自动加载图标
watch(() => props.visible, (newVisible) => {
  if (newVisible && lucideIcons.value.length === 0 && !iconsLoading.value) {
    fetchLucideIcons()
  }
  if (!newVisible) {
    resetState()
  }
}, { immediate: true }) // 立即执行一次，检查初始状态

// 暴露方法给父组件
defineExpose({
  fetchLucideIcons,
  resetState,
  selectedIcon: readonly(selectedIcon),
})
</script>

<style lang="scss" scoped>
.icon-selector {
  max-height: 300px;
  overflow-y: auto;
}
</style>
