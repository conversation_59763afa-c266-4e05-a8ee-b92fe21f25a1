export default {
  stripeView: {
    payNormalTip: '收到付款! 点击“继续”开始使用您的 Juchats 高级账户。',
    payErrorTip: '该笔订单支付异常，请联系客服',
    buttonText: '继续',
  },
  oauthView: {
    bindSucceed: '绑定成功',
  },
  audioRecorder: {
    noSoundDetected: '未检测到声音，请重新录制',
    permissionsRejected: '无法访问麦克风，请检查权限设置',
  },
  googleOauth: {
    tip: 'Google Redirecting...',
    loginRedirecting: '登录失效，将为您自动跳转登录页',
  },
  chatView: {
    placeholder: '请向我提问「{contextTitle}」的问题',
    fileTypeMessage: '当前文件类型不支持上传附件',
    modeMessage: '当前选择模型不支持上传附件',
    cancelStarText: '取消星标成功',
    saveStarText: '保存星标成功',
    upperLimitText: '已达星标上限',
    moveGroupText: '加入分组成功',
    loading: '生成中，请稍候...',
    dialogPlaceholder: '请输入对话名称',
    groupPlaceholder: '请输入分组名称',
    warning: '异常',
    fluxKontextProNoImageError: '请先上传一张图片再进行图生图操作。',
    imageFailedToLoad: '图片加载失败，请稍后重试。',
  },
  egg: {
    title: 'Egg Code',
    message: 'Friends',
    skip: 'Skip',
    placeholder: 'Enter Egg Code',
  },
  baseHeader: {
    title: 'Juchats Hermchats LLM Mixture model Natural language RAG Knowledge',
    twitter: 'Twitter',
    discord: 'Discord',
    home: 'Home',
    login: 'Log In',
  },
  baseFooter: {
    terms: 'Terms',
    privacyPolicy: 'Privacy Policy',
    client: 'Client',
    comingSoon: 'Coming Soon',
    connecting: 'CONNECTING',
  },
  voiceBar: {
    message: '解码音频文件出错',
  },
  userInfo: {
    helpCenter: '帮助中心',
    helpDocs: '帮助文档',
    realtimeStatus: '实时状态',
    client: '客户端下载',
    mobile: '移动端',
    layOut: '安全退出',
    themeSetting: '主题设置',
    light: '明亮',
    dark: '暗黑',
    followSystem: '跟随系统',
    codeExchange: '彩蛋兑换',
    myInvite: '我的邀请',
    detail: '套餐订阅',
    myProfile: '个人资料',
    updateLog: '更新日志',
    languageSettings: '语言设置',
    simplifiedChinese: '简体中文',
    english: '英语',
    userCenter: '个人中心',
    appleSilicon: '苹果芯片',
    appleInter: '英特尔芯片',
    windows: 'Windows',
  },
  usageStatistics: {
    title: 'Statistics',
  },
  sideBar: {
    normalDialog: '常规对话',
    groupDialog: '分组对话',
    createDialog: '创建新对话',
    clear: '清空',
    edit: '编辑',
    cancel: '取消',
    remove: '删除',
    share: '分享',
    update: '修改',
    moveToGroup: '移动到分组',
    resetTitle: '重置标题',
    resetTitleEmitSuccess: '正在重置标题...',
    dialogPlaceholder: '输入对话名称',
    removeTitle: '分组删除，记录即将回归常规对话',
    confirm: '确认',
    groupPlaceholder: '输入分组名称',
    addGroup: '添加分组',
    removeDialog: '删除对话',
    willRemove: '将删除',
    irreversible: '对话，此操作不可逆',
    deleteConfirmation: '是否删除',
    allConversations: '所有',
    deletePart: '是否删除Chat下面部分对话？',
    someConversations: '部分',
    conversations: '对话',
    below: '下面',
    shareDialog: '分享对话',
    shareChat: '分享对话',
    shareWebsite: '分享网页',
    shareSelectorDialog: '分享对话',
    shareSelectorDialogCreate: '创建链接',
    shareSelectorDialogDescription: '您的私密信息在共享后，添加的任何消息都将予以保密处理。',
    starDialog: '星标对话',
    defaultGroup: '默认分组',
    shareDialogSubtitle: '对话即将分享并公开',
    contentAnalyze: '搜索结果',
    deleteAllListEmpty: '列表为空或所有对话都处于分组中，没有可以删除的对话',
  },
  shareFooter: {
    prompt: 'PROMPT',
    views: '浏览次数',
    cumulativeGain: '累计收益',
    unlimitedLove: '无限热爱',
    guest: 'Trial',
    deleted: '该对话已删除',
  },
  sendBox: {
    speechRecognition: '正在识别语音中',
    maximumLimit: '已超出最大字数限制',
    record: '长按语音按钮进行录制',
    tip: '*Stability模型只支持使用英文 Promot 生成图像',
    exceed: '输入内容超过限制',
  },
  sendAttachment: {
    unsupportedFormat: '不支持的文件格式',
    exceed: '文件最大不能超过15MB',
    imgExceed: '图片大小需<5MB',
    uploadFail: '上传失败',
    loginFail: '登录失效，将为您自动跳转登录页',
    cancelParsing: '附件“{filename}”的解析已取消',
    notSupported: '当前模型不支持上传附件',
    onlyImage: '当前模型仅支持上传图片',
    onlyFile: '当前模型仅支持上传文件',
    pdfTooLong: 'PDF文件不能超过%count页',
  },
  sceneButton: {
    rolePlaying: '角色扮演',
  },
  packageBox: {
    confirmPayment: '确认支付',
    waitPayment: '等待支付',
    cancelPayment: '取消',
    renewalPurchase: '续费购买',
    jointPurchase: '联名购买',
    buyNow: '升级套餐',
    currentMember: '当前套餐',
    upgraded: '已升级',
    orderNumber: '订单编号',
    packageCredit: '套餐抵扣',
    rewardCredit: '奖励抵扣',
    actualPayments: '实际付款',
    newUserDiscount: '新用户折扣',
    discountRegularUsers: '老用户折扣',
    packageSubscription: '套餐订阅',
    eggCodeTip: '请输入彩蛋码',
    exchange: '兑换',
    congratulate: '恭喜您，成为万众瞩目的 { title } 用户！',
    subscriptionPackages: '订阅套餐',
    subscriptionTerm: '订阅期限',
    cutOffDate: '截止日期',
    dailyPackage: '日套餐',
    monthlyPackage: '月套餐',
    histories: '历史',
    starburst: '星标',
    group: '分组',
    freeMember: '体验会员',
    basicMember: '体验会员',
    premiumMember: '高级会员',
    extremeMember: '极限会员',
    stripe: 'stripe',
    incentive: '奖励金',
    tipTitle: '提示：',
    planTip: '%title套餐为%type计划，每隔%days个自然日重置！',
    freeType: '免费',
    paidType: '付费',
    expiryTime: '到期',
  },
  notFound: {
    tip: '很遗憾让你来到这个页面看到错误页，因开发资源有限，移动页面将会在晚些时间呈现，敬请期待，以下',
    eggCode: '彩蛋码',
    copyIt: '复制使用！',
    noInviteCode: '目前已无可用邀请码',
    error: 'error',
  },
  newPackageBox: {
    eachTime: '次',
    month: '月',
    day: '天',
  },
  modelSelect: {
    modelName: '模型包含',
    deepThinking: '深度思考',
    remainingCalls: '该模型剩余调用次数',
  },
  loginWays: {
    login: 'login',
    email: 'email',
    emailPlaceholder: 'Enter Your Email Here',
    loginCodePlaceholder: 'Paste login code',
    submitButtonText: 'Continue with email',
    otherLoginText: 'Other Login Options',
    sendEmailMessage: '邮箱验证码已发送，请在5分钟内使用！',
    googleText: 'Continue with Google',
    githubText: 'Continue with Github',
    emailText: 'Sign Up with Email',
    emailFormatTip: '请输入有效的邮箱地址',
  },
  loginBox: {
    mobileLabel: '手机号码',
    mobilePlaceholder: '请输入手机号码',
    codeLabel: '短信验证码',
    codePlaceholder: '请输入短信验证码',
    sendAgain: '重新发送 {seconds}s',
    getCode: '获取验证码',
    inviteCode: '邀请码',
    agree: '勾选同意协议',
    terms: 'Terms',
    policy: 'Policy',
    loginNow: '立即登录',
    mobileFormatTip: '请输入正确的手机号',
    codeFormatTip: '请输入正确的验证码',
    agreeTip: '请阅读并勾选《用户隐私 / 使用 / 免责声明》',
    newUserLoginTip: '新用户请使用 Google、GitHub、Email 授权注册！',
    ttpLoginTip: '您已绑定三方登录，请使用 Google、GitHub、Email 登录！',
    codeTip: '短信验证码已发送，请在5分钟内使用！',
  },
  imageGallery: {
    copy: '复制',
    copySuccess: '复制成功！',
    copyText: '复制文本',
    closeButton: '关闭 (Esc)',
    downloadButton: '下载图片',
    copyImageButton: '复制图片',
  },
  headerMore: {
    export: '导出',
    generatePNG: '导出中...',
    downloadPNG: '文件已经下载',
    generatePDF: '导出中...',
    downloadPDF: '文件已经下载',
    downloadSuccess: '导出成功',
    noTable: '当前对话没有可导出的表格',
    downloadError: '导出失败',
  },
  headerBar: {
    communityBuilding: '目前用户量暴增，资源紧张，正在升级中...',
    updateLog: '更新日志',
    profileData: '资料',
    inviteAction: '邀请',
    manageSubscription: '订阅',
    discoverEasterEgg: '彩蛋',
    actionLogout: '退出',
    viewStatistics: 'Statistics',
    userProfile: '我的资料',
    actionUnbind: '解绑',
    notBoundEmail: '暂未绑定邮箱',
    labelEmail: '邮箱',
    actionBindEmail: '绑定邮箱',
    sendCode: '发送验证码',
    enterYourEmail: '请输入邮箱',
    enterCode: '请输入验证码',
    resendCode: '重新发送 {sendSeconds}s',
    resendNow: '重新发送',
    labelPhone: '手机',
    confirmUnbinding: '确认解绑',
    doNotSave: '暂不保存',
    saveInfo: '保存信息',
    membershipLevel: '会员等级',
    dailyConversationLimit: '每日限量对话次数',
    modelUsageRestriction: '模型使用限制',
    unlimitedConversations: '对话次数不限量',
    priorityOtherModels: '其他模型优先权',
    betaFeatureAccess: '内测功能体验权',
    subscriptionOffer: '订阅套餐优惠权',
    subscriptionPlans: '订阅套餐',
    unlimitedAccess: '不限次数',
    daysValidity: '{ days }天有效',
    payImmediately: '立即支付',
    currentPlan: '当前套餐',
    choosePaymentMethod: '支付方式',
    planPayment: '套餐支付',
    orderDetails: '订单信息',
    paymentTimer: '付款计时',
    packageOffset: '套餐抵扣',
    dailyPackageDeduction: '（原套餐按日抵扣）',
    rewardOffset: '奖励抵扣',
    unitCurrency: '元',
    labelReward: '奖励',
    weChatPayment: '微信支付',
    alipayPayment: '支付宝支付',
    subscriptionStatus: '订阅状态',
    plusUserWelcome: '恭喜您，成为万众瞩目的Plus用户！',
    orderStatus: '订单状态',
    paymentStatus: '支付状态',
    paymentTotal: '支付金额',
    numberInvited: '邀请人数',
    myReward: '我的奖励',
    myInviteCode: '我的邀请码',
    copyInviteLink: '复制链接',
    nicknameUser: '用户昵称',
    buySubscriptionPlan: '购买套餐',
    receiveReward: '获得奖励',
    timeOfPurchase: '购买时间',
    brandReconstruction: '品牌 重构品牌识别 和 <span class="red">Landing</span> 页面',
    modelSupportGPT: '模型 支持 <span class="green">GPT Forte</span> 混合模型',
    modelSupportClaude: '模型 支持 <span class="green">Claude Forte</span> 混合模型',
    modelSupportMixtral: '模型 支持 <span class="green">Mixtral Forte</span> 混合模型',
    modelSupportDALL: '模型 支持 DALL·E3 独立使用',
    modelSupportLlama: '模型 支持 Llama3 70B 体验使用',
    optimizeLeTax: '优化 LeTax 公式展示样式',
    optimizeMarkdown: '优化 Markdown 及 代码块展示样式',
    optimizeConnection: '优化 联网速度和准确性',
    fixBugs: '修复 若干Bug及体验问题 <span class="blue">via. Discord</span>',
    subscriptionOpen: '订阅 开启部分套餐订阅',
    subscriptionUnlimited: '订阅 Plus 及更高级会员联网无限次数使用',
    subscriptionHistory: '订阅 Plus 及更高级会员历史记录无次数限制',
    paymentProcessing: '支付中',
    paymentSuccessful: '支付成功',
    paymentCancelled: '取消支付',
    transactionClosed: '交易关闭',
    refundedAmount: '已退款',
    contactSupport: '联系客服',
    phoneFormatError: '手机格式错误',
    successfulUnbind: '解绑成功',
    enterCorrectEmailAccount: '请输入正确的邮箱账号',
    successfulBinding: '绑定成功',
    confirmUnbindPhone: '* 点击"确认解绑"即可解绑删除手机号',
    successfulModification: '修改成功',
    infoSavedSuccessfully: '保存成功',
    free: 'Free',
    plus: 'Plus',
    avatarLimit: '头像大小不能超过5MB',
    avatarFormat: '头像必须是JPG或PNG格式',
    avatarSuccess: '头像上传成功',
    avatarError: '头像上传失败',
  },
  footerBar: {
    privacyPolicy: 'Privacy policy',
    termsUse: 'Terms of use',
  },
  emailBox: {
    emailAddress: '邮箱账号',
    promptEnterEmail: '请输入邮箱账号',
    emailVerificationCode: '邮箱验证码',
    promptEnterVerificationCode: '请输入邮箱验证码',
    promptEnterInvitationCode: '请输入邀请码',
    agreeToTerms: '勾选同意',
    fillEmailAddress: '请填写邮箱账号',
    fillVerificationCode: '请填写验证码',
  },
  eggRedemption: {
    eggCodeExchange: '彩蛋码兑换',
    eggCodePlaceholder: '请输入彩蛋码',
    exchange: '兑换',
  },
  dragAttachmentPlaceHolder: {
    placeholder: '拖拽文件到对话框，最多{ ATTACHMENTS_NUMBER_LIMIT }个文件，单个文件需',
  },
  dialog: {
    confirm: '确 定',
  },
  controlBar: {
    clear: '清空',
    confirmText: '是否删除所有对话？',
    clearDialogTitle: '清空对话',
    clearDialogSuccess: '清空成功',
    clearDialogContent: '确定要清空当前对话上下文吗？此操作不可恢复。',
  },
  chatContent: {
    regenerate: '重新生成',
    networkSearch: '联网搜索',
    relatedQuestions: '关联提问',
    question: 'Question',
    image: 'Image',
    errorText: '抱歉，当前对话出现了些异常',
    errorButton: '点击重试',
    viewFullMessage: '查看完整消息',
    mindMap: '思维导图',
    editorItem: '编辑',
    editorItemSave: '保存',
    editorItemSend: '发送',
    noContentModification: '无任何内容修改',
    invalidContent: '内容不能为空',
    lossContent: '缺少联网数据',
    dataError: '对话失败或超时',
    toggleRenderMode: '切换渲染模式',
  },
  bindLogin: {
    authLogin: 'OAuth Login',
    googleAuthorized: 'Google authorized',
    githubAuthorized: 'Github authorized',
    bind: '绑定',
    googleTip: '我们建议使用 Google 授权登录，安全快捷，这是产品去中心化的第一步！',
  },
  billDetail: {
    start: '开启旅程',
    serialNumber: 'SERIAL NUMBER',
  },
  attachmentCard: {
    parseFailure: '解析失败',
    parsing: '正在解析',
    parseSuccess: '解析成功！',
  },
  personalCenterInfo: {
    nickName: '昵称',
    unbindedEmail: '暂未绑定邮箱',
    unbindedLogin: '暂未绑定该登录方式',
    save: '保存',
  },
  personalCenterInvitation: {
    userAccount: '用户账号',
    rewards: '奖励金额',
  },
  personalCenterLayout: {
    mySubscription: '我的订阅',
    myCenter: '个人中心',
  },
  personalCenterSubscription: {
    status: '当前状态',
    payAmount: '付款金额',
    payTime: '付款时间',
    relatedOperations: '相关操作',
    pay: '支付',
    view: '查看',
  },
  eGGSuccess: {
    greet: 'Hi,',
    congratulations: 'Congratulations',
    subscribePackage: 'Subscribe Package',
    cycle: 'Cycle',
    deadline: 'Deadline',
    serialNumber: 'serial number',
  },
  tipMessage: {
    copyLinkSuccess: '复制链接',
    loginFail: '登录失效，将为您自动跳转登录页',
    upgradeTip: '当前套餐次数已用完,',
    upgrade: '请升级会员',
    error: '系统异常，请稍后重试',
    copySuccess: '复制成功',
    copyTextSuccess: '复制文字成功',
    copyMarkdownSuccess: '复制Markdown成功',
    copyFail: '复制失败',
    imgPlaceholder: '请输入要生成图片的描述',
    filePlaceholder: '请输入你的问题，文件上传支持选择、粘贴、拖拽',
    questionPlaceholder: '请输入你的问题',
    oneDay: '一天',
    fiveDays: '五天',
    sevenDays: '七天',
    fifteenDays: '十五天',
    oneMonth: '一个月',
    oneYear: '一年',
    day: '天',
    expiry: '订阅已到期',
    renewal: '立即续费',
    llmStreaming: '正在生成中，请稍候...',
    saveSuccess: '保存成功',
    saveFailed: '保存失败',
  },
  tools: {
    MERMAID: 'Mermaid图表生成',
    IMAGE_CREATION: '图片生成',
    DATA_ANALYSIS: '数据分析',
    BROWSING: '在线搜索',
    SEARCH_URL: '检索网址',
    SEARCH_FILES: '检索文件',
    SUMMARIZE_URL: '总结网址',
    SUMMARIZE_FILE: '总结文件',
    X_INFO: '𝕏搜索',
    MAP_GENERATION: '地图生成',
    ARTIFACTS: 'Artifacts生成',
    CHOOSE_TOOLS: '激活工具中',
    NETWORK_UNSTABLE: '您当前网络波动，正在魔法修复，不要走开',
    NETWORK_UNSTABLE_ERROR: '请求异常，请稍后再试',
    NETWORK_TIMEOUT_ERROR: '网络请求超时，请稍后重试',
    NETWORK_MAX_RETRIES_ERROR: '重试次数过多，请稍后重试',
    TOOL_CALL_TIMEOUT: '你要召唤的工具罢工了，请刷新后重新尝试',
    CHEMISTRY: '绘制化学式中',
    STRUCTURAL_BIOLOGY: '查找相关信息中',
  },
  guildPrompt: {
    summaryTweets: '总结推文',
    appleFinancial: '苹果年度财报',
    mermaid: '关系图谱',
    createImage: '创建图像',
    travelPlan: '旅行计划',
    documentInterpretation: '文件上传解析',
    summaryLink: '文章链接总结',
    summaryTweetsPrompt: '{\'展示 @Cydiar404 前十条推文（图文），并进行腹黑点评！\'}',
    appleFinancialPrompt: '2024最新苹果 10-K 财报分析及趋势图表绘制',
    mermaidPrompt: '帮我通过关系图谱梳理出百年孤独的时间线和人物关系',
    createImagePrompt: '创建一张梵高星空风格的夕阳落日！横向的！',
    travelPlanPrompt: '上海三日游推荐路线，并通过地图标注出来！',
    documentInterpretationPrompt: '精读《底层逻辑》并总结！',
    summaryLinkPrompt: 'https://medium.com/vectrix-ai/how-to-implement-a-react-flow-using-langgraph-studio-5e4b859b5506\n\n深度总结以上链接',
    mixedMode: '内容混排模式',
    mixedModePrompt: 'https://timkellogg.me/blog/2025/02/03/s1\n深度总结这篇文章，需要非常细致，获取图片完整链接显示在必要的地方! ',
    twitterThread: '推特长文整理',
    twitterThreadPrompt: '请获取「https://x.com/omarsar0/status/1891705029083512934」这篇推文所有的thread，无需总结，返回全部原文，保留 Markdown 格式',
  },
  markMap: {
    title: '思维导图',
    zoomIn: '放大',
    zoomOut: '缩小',
    fitScreen: '适应屏幕',
    download: '下载',
    fullscreen: '全屏',
    exitEditMode: '退出编辑模式',
    enterEditMode: '编辑模式',
  },
  artifactsCodeHtml: {
    screenshotGenerating: '生成中，请稍候',
    screenshotSuccess: '生成成功',
    close: '关闭',
    fullScreen: '全屏',
    download: '下载',
    screenshot: '截图',
    elementPicker: '元素选择',
    lockedElement: '当前元素已锁定',
    elementDescriptionPlaceholder: '请输入关于此元素的指令或描述...',
    saving: '保存中...',
    parseError: '解析 Artifacts 数据失败',
    unknownFormat: '未知的 Artifacts 数据格式',
    enterEditMode: '进入图片文字编辑模式',
    exitEditMode: '退出编辑图片文字模式',
    saveAndGetHtml: '保存',
    imageEditTitle: '编辑图片',
    imageEditPlaceholder: '请输入图片URL地址...',
    imageEditConfirm: '确定',
    imageEditCancel: '取消',
    imageEditUploadText: '点击选择图片文件',
    imageEditUploading: '上传中...',
    imageEditUploadSuccess: '上传成功',
    imageEditUploadFailed: '上传失败',
    imageEditTabUrl: 'URL地址',
    imageEditTabUpload: '上传文件',
    imageEditTabIcon: '选择图标',
  },
  iconSelector: {
    loadingIcons: '正在加载图标...',
    retryLoadIcons: '重新加载',
    iconSearchPlaceholder: '搜索图标...',
    noIconsFound: '未找到匹配的图标',
    tryDifferentKeywords: '请尝试其他关键词',
    loadMoreIcons: '加载更多图标',
    totalIcons: '共 {count} 个图标，还剩 {left} 个未加载',
  },
  updateNote: {
    title: '更新日志',
  },
  claude4thinking: '正在思考...',
  chatHistoryList: {
    all: '全部',
    today: '今天',
    week: '本周',
    month: '本月',
    earlier: '更早之前',
  },
  proteinPanel: {
    title: '蛋白质信息',
    tabs: {
      info: '蛋白质信息',
      details: '详细信息',
      validation: '验证报告',
    },
    loadingDetails: '正在加载详细信息...',
    loadingValidation: '正在检查验证报告...',
    errors: {
      loadFailed: '暂无相关信息',
      validationUnavailable: '验证报告不可用',
      validationUnavailableDesc: '该PDB结构暂无验证报告',
    },
    noData: {
      title: '暂无蛋白质信息',
    },
    basicInfo: {
      title: '【基本信息】',
      structureTitle: '结构标题',
      pdbId: 'PDB ID',
      keywords: '关键词',
      unknown: '未知',
    },
    publication: {
      title: '【发表文献】',
      articleTitle: '文献标题',
      authors: '作者',
      journal: '期刊',
      doi: 'DOI',
    },
    experimental: {
      title: '【实验信息】',
      method: '实验方法',
      resolution: '分辨率',
      composition: '分子组成',
      releaseDate: '发布日期',
    },
    validation: {
      title: '【验证报告】',
      openInNewWindow: '在新窗口打开',
      pdfTitle: 'PDB验证报告',
      unavailable: '验证报告不可用',
      unavailableDesc: '该PDB结构暂无验证报告',
    },
  },
}
