import type { Meta, StoryObj } from '@storybook/vue3'
import IconSelector from './IconSelector.vue'

const meta: Meta<typeof IconSelector> = {
  title: 'component/IconSelector',
  component: IconSelector,
  tags: ['autodocs'],
  argTypes: {
    visible: {
      control: 'boolean',
      description: '是否显示图标选择器',
    },
  },
  args: {
    visible: true,
    onSelect: (iconName: string) => {
      console.log('Selected icon:', iconName)
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    visible: true,
  },
  render: (args) => ({
    components: { IconSelector },
    setup() {
      const selectedIcon = ref('')
      
      const handleIconSelect = (iconName: string) => {
        selectedIcon.value = iconName
        console.log('Selected icon:', iconName)
      }
      
      return { 
        args,
        selectedIcon,
        handleIconSelect,
      }
    },
    template: `
      <div class="p-20px">
        <h3 class="mb-16px text-lg font-semibold">图标选择器演示</h3>
        <div class="mb-16px">
          <span class="text-sm text-gray-600">当前选中的图标: </span>
          <span class="font-mono text-blue-600">{{ selectedIcon || '未选择' }}</span>
        </div>
        <div class="border border-gray-200 rounded-lg p-16px bg-white">
          <IconSelector 
            v-bind="args"
            @select="handleIconSelect"
          />
        </div>
      </div>
    `,
  }),
}

export const WithLucideIcons: Story = {
  args: {
    visible: true,
  },
  render: (args) => ({
    components: { IconSelector },
    setup() {
      const selectedIcon = ref('')
      const iconSelectorRef = ref()
      
      const handleIconSelect = (iconName: string) => {
        selectedIcon.value = iconName
        console.log('Selected Lucide icon:', iconName)
      }
      
      onMounted(() => {
        // 确保选择 Lucide 图标库
        if (iconSelectorRef.value) {
          iconSelectorRef.value.selectLibrary('lucide')
        }
      })
      
      return { 
        args,
        selectedIcon,
        handleIconSelect,
        iconSelectorRef,
      }
    },
    template: `
      <div class="p-20px">
        <h3 class="mb-16px text-lg font-semibold">Lucide 图标库演示</h3>
        <div class="mb-16px">
          <span class="text-sm text-gray-600">当前选中的图标: </span>
          <span class="font-mono text-blue-600">{{ selectedIcon || '未选择' }}</span>
        </div>
        <div class="border border-gray-200 rounded-lg p-16px bg-white">
          <IconSelector 
            ref="iconSelectorRef"
            v-bind="args"
            @select="handleIconSelect"
          />
        </div>
      </div>
    `,
  }),
}

export const WithRemixIcons: Story = {
  args: {
    visible: true,
  },
  render: (args) => ({
    components: { IconSelector },
    setup() {
      const selectedIcon = ref('')
      const iconSelectorRef = ref()
      
      const handleIconSelect = (iconName: string) => {
        selectedIcon.value = iconName
        console.log('Selected Remix icon:', iconName)
      }
      
      onMounted(() => {
        // 确保选择 Remix Icon 图标库
        if (iconSelectorRef.value) {
          iconSelectorRef.value.selectLibrary('remix')
        }
      })
      
      return { 
        args,
        selectedIcon,
        handleIconSelect,
        iconSelectorRef,
      }
    },
    template: `
      <div class="p-20px">
        <h3 class="mb-16px text-lg font-semibold">Remix Icon 图标库演示</h3>
        <div class="mb-16px">
          <span class="text-sm text-gray-600">当前选中的图标: </span>
          <span class="font-mono text-blue-600">{{ selectedIcon || '未选择' }}</span>
        </div>
        <div class="border border-gray-200 rounded-lg p-16px bg-white">
          <IconSelector 
            ref="iconSelectorRef"
            v-bind="args"
            @select="handleIconSelect"
          />
        </div>
      </div>
    `,
  }),
}
