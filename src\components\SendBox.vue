<template>
  <div
    class="mx-auto mt-6 max-w-600px w-[calc(100%-48px)] flex items-center gap-13px"
    :class="{
      '!max-w-327px md:!max-w-360px': recording,
      'chat_item_margin': isResizablePanelVisible,
    }"
  >
    <!-- :class="{ '!sticky !bottom-60px': focus && !isRealPc }" -->
    <Transition name="fade" mode="out-in">
      <div
        v-if="[0, 1, 2, 3, 4].includes(selected)"
        class="send-box relative flex flex-1 items-center"
        :class="{
          'send-box--focus': focus,
          'show-attachment': showAttachment,
          'show-control': loading,
        }"
      >
        <Transition enter-active-class="animate__animated animate__tada">
          <SendAttachmentPreview
            v-if="showAttachment" :class="[
              loading ? 'bottom-[calc(100%+60px)]' : 'bottom-[calc(100%+15px)]',
            ]"
          />
        </Transition>
        <Transition name="cus-transition" mode="out-in">
          <SendAttachmentDecorate v-if="shouldShowDecorate" class="absolute bottom-[calc(100%+16px)] right-16px" />
        </Transition>
        <Transition enter-active-class="animate__animated animate__heartBeat">
          <div v-if="showAttachment && !recording" ref="attachmentIcon" class="absolute left-13px z-2 text-22px lt-md:(left-3px)">
            <SendAttachment v-model:is-success="uploaded" class="flex-y-c" @success="onUploadSuccess" @audio-transcription="onAudioTranscription"></SendAttachment>
          </div>
        </Transition>

        <div v-if="!recording" class="relative relative flex-y-c flex-1">
          <div v-if="IsCelHiveLinkIn" class="absolute left-0 top-[-40px]">
            <BrowserTabs :tabs="tabs" />
          </div>
          <textarea
            ref="textarea"
            lang="en"
            class="text-main relative z-1 max-h-200px min-h-30px w-full break-words bg-transparent py-5px pl-50px pr-100px text-14px leading-normal outline-none transition-cusbezier-300 lt-md:(min-h-23px px-20px py-2px)"
            :class="{
              '!pl-10px': !showAttachment && !isPc,
              '!pl-20px': !showAttachment && isPc,
              '!pl-40px': !isPc,
              '!pr-80px': !isPc,
              '!pb-20px': textareaHeight >= 160 && isPc,
            }"
            :rows="1"
            :value="modelValue"
            :disabled="loading || !modelReady"
            :placeholder="placeholder"
            :maxlength="maxLength"
            autofocus
            type="text"
            @input="onInput"
            @focus="onFocus"
            @keydown.enter="onEnter"
            @blur="onBlur"
            @paste="onPaste"
            @scroll="syncScroll"
          />

          <div
            ref="suggestionRef"
            lang="en"
            class="suggestion pointer-events-none absolute left-0 top-0 z-0 max-h-200px min-h-30px w-full overflow-y-auto whitespace-pre-wrap break-words py-5px pl-50px pr-140px text-14px text-[#999] leading-normal dark:!text-[#666]"
            :class="{
              '!pl-10px': !showAttachment && !isPc,
              '!pl-20px': !showAttachment && isPc,
              '!px-40px': !isPc,
              '!pb-20px': suggestionHeight >= 160 && isPc,
            }"
          >
            <span class="invisible">{{ modelValue }}</span>
            <span v-if="modelValue.trim() !== '' && cycleState" class="ml-6px">{{ cycleState }}</span>
          </div>
        </div>

        <!-- 字数限制显示，先不要 -->
        <div
          v-if="(!isVoice && isPc) && shouldShowTextLength && !recording"
          class="absolute-y-center right-55px overflow-hidden text-12px text-[#a8a8a8] transition-cusbezier-300" :class="{
            'right-70px bottom-[calc(-100%-40px)]':
              textLengthPosition === 'BOTTOM',
          }"
        >
          <span v-if="tooLong()" class="pl-[20px] text-[#ff0000]">
            {{ $t("sendBox.maximumLimit") }}
          </span>
          <span :class="{ 'text-[#ff0000]': tooLong() }">{{
            modelValue.length
          }}</span>
          <span> / </span>
          <span class="text-send-box-character-limit font-['Roboto_Slab']">{{
            maxLength
          }}</span>
        </div>
        <div v-if="showSuggestion && !recording" class="pointer-events-none absolute-y-center right-55px">
          <div class="flex-y-c gap-6px">
            <div class="suggestion-button size-18px">
              <i class="i-ju-sendbox-button-arrow text-7px transition-cusbezier-300 hover:op-70" />
            </div>
            <div class="suggestion-button size-18px">
              <i class="i-ju-sendbox-button-arrow rotate-180 text-7px transition-cusbezier-300 hover:op-70" />
            </div>
            <div class="suggestion-button h-18px px-7px">
              <span class="text-10px">Tab</span>
            </div>
          </div>
        </div>
        <div class="send-box__suffix flex-y-c">
          <AudioTranscribe v-if="isNotProduction && Boolean(modelValue.trim()) === false && !loading" :loading="transcriptioning" @start="onStart" @stop="onStop" @recording-complete="onRecordingComplete" @error="onError" />
          <template v-else>
            <el-button
              v-if="!scene && loading"
              circle
              :color="isDark ? '#fff' : '#000'"
              size="small"
              class="send_btn hover:op-70"
              @click="breakChat"
            >
              <i class="i-material-symbols-stop-rounded text-18px" />
            </el-button>
            <el-button
              v-else
              circle
              :color="isDark ? '#fff' : '#000'"
              :disabled="disabled"
              size="small"
              class="send_btn"
              @click="onEnter"
            >
              <i class="i-ju-sendbox-button-arrow text-10px" />
            </el-button>
          </template>
        </div>
        <div
          v-if="!scene"
          v-show="!loading"
          ref="controlRef" :style="{ top: controlTop }"
          class="send-box__control flex flex-wrap items-center gap-12px"
        >
          <template
            v-if="
              [MODE_TYPE.E3, MODE_TYPE.STABLE, MODE_TYPE.IDEOGRAM].includes(modeType)
                && !loading"
          >
            <SimpleButton
              v-for="(item, index) in sizes"
              :key="index"
              class="rounded-4px transition-cusbezier-150 !h-30px space-x-4px"
              :class="[
                item === size && '!text-[var(--black)]',
              ]"
              @click="setSize(item)"
            >
              <span>{{ item }}</span>
              <div
                class="w-0 overflow-hidden transition-cusbezier-250"
                :class="{ '!w-16px': item === size }"
              >
                <i class="i-ri-check-fill text-15px"></i>
              </div>
            </SimpleButton>
          </template>
        </div>
        <Transition enter-active-class="animate__animated animate__fadeInUp">
          <div
            v-if="[13].includes(modeType) && !loading"
            class="absolute bottom-[-35px] w-full text-center text-[12.5px] text-[#a8a8a8]"
          >
            {{ $t("sendBox.tip") }}
          </div>
        </Transition>
      </div>
    </Transition>
    <!-- <mobile>
      <TTS />
    </mobile> -->
  </div>
</template>

<script setup lang="tsx">
import { watchOnce } from '@vueuse/core'
import { debounce } from 'lodash'
import { getActualLength } from '@/common'
import service from '@/common/axios'
import { notify, requestPlugin } from '@/common/tools'
import BrowserTabs from '@/components/BrowserTabs.vue'
import SendAttachment from '@/components/SendAttachment.vue'
import { mixpanel } from '@/config/mixpanel'
import { MODE_TYPE } from '@/enum'
import { useComposition } from '@/hooks/useComposition'
import { useDevice } from '@/hooks/useDevice'
import { useGlobalI18n } from '@/hooks/useGlobalI18n'
import { useThemeChange } from '@/hooks/useThemeChange'
import { useAppStore } from '@/stores/app'
import { useCelHiveLink } from '@/stores/celhive-link'
import { useChatStore } from '@/stores/chat'
import { base64ToFile } from '@/utils'
import AudioTranscribe from './AudioTranscribe.vue'

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: '',
  },
  modelValue: {
    type: String,
    default: '',
  },
  maxToken: {
    type: Number,
    default: undefined,
  },
  selected: {
    type: Number,
    default: null,
  },
  uuid: {
    type: String,
    default: '',
  },
  size: {
    type: String,
    default: '1:1',
  },
  ttsId: {
    type: Number,
    default: null,
  },
  voice: {
    type: Array,
    default: () => [],
  },
  scene: {
    type: Boolean,
    default: false,
  },
  modeType: {
    type: Number,
    default: null,
  },
})
const emits = defineEmits([
  'update:modelValue',
  'update:uuid',
  'update:size',
  'update:voice',
  'enter',
  'scene',
  'restart',
  'onAssistantMessage',
  'onSilenceAction',
  'onReceiveDone',
  'breakChat',
])
const chatStore = useChatStore()

const t = useGlobalI18n()
const pluginAgent = useCelHiveLink()
const { IsCelHiveLinkIn, tabs } = storeToRefs(useCelHiveLink())

const controlRef = ref<HTMLElement | null>(null)
const controlTop = ref('-50px')

const { height: controlHeight } = useElementSize(controlRef)
const { selectedMode, canUploadAttachment } = storeToRefs(useAppStore())
const { messageCounter } = storeToRefs(chatStore)

const checkControlOverflow = () => {
  if (controlRef.value) {
    const control = controlRef.value
    const children = Array.from(control.children) as HTMLElement[]

    if (children.length > 0) {
      const firstChildTop = children[0].offsetTop
      const lastChildBottom
        = children[children.length - 1].offsetTop
          + children[children.length - 1].offsetHeight
      const totalHeight = lastChildBottom - firstChildTop

      // 计算行数（考虑 gap）
      const childHeight = children[0].offsetHeight
      const gap = 12 // gap 值
      const lineHeight = childHeight + gap
      const lineCount = Math.round((totalHeight + gap) / lineHeight)

      // 根据行数动态调整 top 值
      const baseTop = -50 // 基础 top 值
      const additionalTopPerLine = -40 // 每增加一行额外增加的 top 值
      const newTop = baseTop + (lineCount - 1) * additionalTopPerLine

      controlTop.value = `${newTop}px`
    }
  }
}

// 使用 watchThrottled 来监听高度变化
watchThrottled(
  controlHeight,
  () => {
    nextTick(checkControlOverflow)
  },
  { throttle: 200 },
)
watch(
  () => props.modeType,
  () => {
    // 切换模型
    setSize('1:1')
  },
)
const shouldShowTextLength = false

// 监听输入框内容变化，清空建议列表
watch(
  () => props.modelValue,
  () => {
    cycleList.value = []
  },
)

const { showAttachment, attachments, isPc, isNotProduction, isResizablePanelVisible } = storeToRefs(useAppStore())
const { isRealPc } = useDevice()
const uploaded = ref(false)
const textLengthPosition = ref('RIGHT')
const { isDark } = useThemeChange()
const textarea = ref()
const recording = ref(false)
const transcriptioning = ref(false)
const focus = defineModel('focus', { type: Boolean, default: false })
const sizes = computed(() => {
  if (props.modeType === 3) {
    return ['1:1', '7:4', '4:7']
  }
  else if (props.modeType === 13) {
    return ['16:9', '1:1', '21:9', '2:3', '3:2', '4:5', '5:4', '9:16', '16:21']
  }
  else if (props.modeType === 20) {
    return ['1:1', '10:16', '16:10', '9:16', '16:9', '3:2', '2:3', '4:3', '3:4', '1:3', '3:1']
  }
  else {
    return ['1:1', '16:9', '2:3', '3:2', '4:5', '5:4', '9:16']
  }
})
const { uploadManually } = useAppStore()
const isParsingFile = computed(() => {
  const found = attachments.value.find((item) => {
    return item.isParsing === true
  })
  return !!found
})

const disabled = computed(() => {
  return (
    !props.modelValue
    || (attachments.value.length > 0 && (!uploaded.value || isParsingFile.value))
  )
})

function onPaste(event: ClipboardEvent) {
  const items = event.clipboardData?.items
  console.log('items', items)
  if (items) {
    const files: File[] = []
    for (const index in items) {
      const item = items[index]
      if (item.kind === 'file') {
        event.preventDefault()
        const file = item.getAsFile()
        if (file) {
          files.push(file)
        }
      }
    }
    if (files.length > 0) {
      uploadManually(files)
    }
  }
}

function onUploadSuccess() {
  // if (props.modelValue.trim() === '' || DEFAULT_PROMPTS.includes(props.modelValue.trim())) {
  //   if (attachmentsRes.value.IMAGE.length > 0) {
  //     emits('update:modelValue', SCAN_DEFAULT_PROMPT)
  //   }
  //   if (attachmentsRes.value.TEXT.length > 0) {
  //     emits('update:modelValue', TEXT_FILE_DEFAULT_PROMPT)
  //   }
  //   if (attachmentsRes.value.DATA.length > 0) {
  //     emits('update:modelValue', DATA_FILE_DEFAULT_PROMPT)
  //   }
  // }
}

const tooLong = () => {
  return maxLength.value && props.modelValue.length > maxLength.value
}

let retry = 0
const setFocus = () => {
  retry++
  if (retry > 100) {
    console.warn('setFocus failed')
    return
  }

  nextTick(() => {
    if (!textarea.value) {
      return
    }

    setTimeout(() => {
      textarea.value && textarea.value.focus()
      if (document.activeElement?.tagName.toLowerCase() !== 'textarea') {
        return setFocus()
      }
    }, 100)
  })
}

const suggestionRef = ref()
const resize = (useSuggestionHeight = false) => {
  nextTick(() => {
    if (textarea.value) {
      textarea.value.style.height = 'auto'
      let newHeight

      if (useSuggestionHeight && suggestionRef.value) {
        // 使用建议文本的高度
        newHeight = Math.min(suggestionRef.value.scrollHeight, 200)
      }
      else {
        // 使用输入框文本的高度
        newHeight = Math.min(textarea.value.scrollHeight, 200)
      }

      textarea.value.style.height = props.modelValue === '' ? 'auto' : `${newHeight}px`
      suggestionRef.value.style.height = `${newHeight}px`
    }

    if (getActualLength(props.modelValue) > (isPc.value ? 55 : 20)) {
      textLengthPosition.value = 'BOTTOM'
    }
    else {
      textLengthPosition.value = 'RIGHT'
    }
  })
}

watch(
  () => props.modelValue,
  () => {
    resize()
  },
)

const isVoice = computed(() => {
  return selectedMode.value ? [MODE_TYPE.LEPTON_AUDIO].includes(selectedMode.value?.type) : false
})

const modelReady = computed(() => {
  return selectedMode.value ? Boolean(selectedMode.value.name) : false
})

const maxLength = computed(() => {
  return props.maxToken && ![MODE_TYPE.E3, MODE_TYPE.FLUXPRO, MODE_TYPE.IDEOGRAM, MODE_TYPE.GPT_4_1, MODE_TYPE.GEMINI_EXP].includes(selectedMode.value?.type || 0) ? props.maxToken / 2 : undefined
})

const onInput = ({ target: { value } }: any) => {
  if (maxLength.value && value.length >= maxLength.value) {
    notify.error({ title: t('sendBox.exceed').value })
  }
  // if (value.length === 1 && value === '/') {
  //   emits('scene', true)
  // }
  emits('update:modelValue', value)
}
const onFocus = () => {
  focus.value = true
  if (!isRealPc.value) {
    setTimeout(() => {
      textarea.value?.scrollIntoViewIfNeeded()
    }, 300)
  }
}

function onBlur() {
  focus.value = false
  cancelGetSuggestion()
}

const { isComposing } = useComposition(textarea)
const onEnter = (event?: KeyboardEvent | Event) => {
  // 1. 基础检查：正在输入中或禁用状态
  if (isComposing.value || disabled.value) {
    return
  }

  // 2. 检查是否超出长度限制
  if (maxLength.value && props.modelValue.length > maxLength.value) {
    return
  }

  // 3. 处理键盘事件
  if (event?.type === 'keydown') {
    const keyEvent = event as KeyboardEvent

    // PC端回车直接发送，移动端回车换行
    if (keyEvent.key === 'Enter' && !keyEvent.shiftKey) {
      if (isRealPc.value) {
        event.preventDefault()
      }
      else {
        resize()
        return
      }
    }

    // Shift+Enter 任何平台都只调整高度不发送
    if (keyEvent.shiftKey) {
      resize()
      return
    }
  }

  // 4. 取消未执行的建议请求
  cancelGetSuggestion()

  // 5. 发送消息
  emits('enter')
}
const breakChat = () => {
  emits('breakChat')
}
const setSize = (string: string) => {
  emits('update:size', string)
}

const attachmentIcon = ref()
const isHoveredSendBox = useElementHover(attachmentIcon, {
  delayLeave: 300,
})

// 是否显示那个装饰
const shouldShowDecorate = computed(() => {
  return isHoveredSendBox.value && !attachments.value?.length && isPc.value && canUploadAttachment.value
})

const cycleList = ref<any>([])
const { state: cycleState, next, prev } = useCycleList(cycleList)

// 添加请求计数器
const requestCount = ref(0)

/**
 * 监听消息发送计数器的变化
 * 这个监听器主要用于监听通过sendMessage发送出去的消息：
 * sendMessage发送的消息不应该触发建议
 * 如果通过sendMessage发送出去的消息会使得messageCounter这个pinia中的变量自增，实现组件监听到有消息发送出去
 *
 * 由于GuildPrompt通过sendMessage来发送消息，GuildPrompt中的发送有500ms延迟，我们这里设置600ms延迟来取消建议：
 * - 确保在实际消息发送后再取消建议
 * - 额外的100ms是为了给网络请求和状态更新留出缓冲时间
 */
watch(messageCounter, () => {
  setTimeout(() => {
    cancelGetSuggestion()
  }, 600)
})

// 用于取消未执行的getSuggestion
const cancelGetSuggestion = () => {
  getSuggestion.cancel()
  cycleList.value = []
}

const getSuggestion = debounce(async (text: string) => {
  if (text.trim() === '') {
    return
  }
  if (!isPc.value) {
    return
  }
  cycleList.value = []
  // 记录当前请求的序号
  const currentRequest = ++requestCount.value
  //  截取最后100字，防止用户输入特别多，输入太长推理不准确
  const last100 = text.slice(-100)
  try {
    const data = await service.post(`/gpt/generateCompletions`, {
      prompt: last100,
    }, {
      baseURL: '/gw/chatgpt',
    })
    // 如果请求回来了，但是输入框为空或者没有focus，则不显示建议
    if (props.modelValue.trim() === '' || !focus.value) {
      cycleList.value = []
      return
    }

    if (currentRequest === requestCount.value) {
      cycleList.value = data
      // 使用建议文本高度调整输入框
      resize(true)
    }
  }
  catch {
    if (currentRequest === requestCount.value) {
      cycleList.value = []
    }
  }
}, 2000)

// 监听输入变化，输入法正在输入时，不触发建议
watchEffect(() => {
  if (props.modelValue && !isComposing.value) {
    getSuggestion(props.modelValue)
  }
})

watch(
  () => cycleList.value,
  () => {
    if (!cycleList.value?.length) {
      nextTick(() => {
        resize()
      })
    }
  },
)
const showSuggestion = computed(() => {
  return props.modelValue.trim() !== '' && cycleList.value.length > 0 && isPc.value
})

onKeyStroke('ArrowDown', (e) => {
  if (showSuggestion.value) {
    e.preventDefault()
    next()
    resize(true)
  }
}, { dedupe: true })

onKeyStroke('ArrowUp', (e) => {
  if (showSuggestion.value) {
    e.preventDefault()
    prev()
    resize(true)
  }
}, { dedupe: true })

onKeyStroke('Tab', (e) => {
  if (showSuggestion.value && focus.value) {
    e.preventDefault()
    emits('update:modelValue', props.modelValue + cycleState.value)
    mixpanel.guideUserUseTabKey()
  }
}, { dedupe: true })

const { height: textareaHeight } = useElementSize(textarea)
const { height: suggestionHeight } = useElementSize(suggestionRef)

function syncScroll(e: Event) {
  if (!suggestionRef.value || !textarea.value) { return }
  suggestionRef.value.scrollTop = (e.target as HTMLTextAreaElement).scrollTop
}

function onAudioTranscription(data: string) {
  emits('update:modelValue', data)
}

function onStart() {
  recording.value = true
}

async function onRecordingComplete(file: File) {
  recording.value = false
  transcriptioning.value = true
  const formData = new FormData()
  formData.append('file', file)
  try {
    const data = await service.post<any, any>('gpt/transcriptions', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      baseURL: '/gw/chatgpt',
    })
    chatStore.sendMessage({
      prompt: data,
    })
  }
  finally {
    transcriptioning.value = false
  }
}

function onError(error: string) {
  notify.error({ title: error })
  recording.value = false
}

function onStop() {
  nextTick(() => {
    recording.value = false
  })
}

defineExpose({
  resize,
  setFocus,
})

pluginAgent.onQuoteTxt((text) => {
  emits('update:modelValue', text)
})

pluginAgent.onPasteImgFn(async () => {
  const clipboardItems = await navigator.clipboard.read()
  const imageFiles = []
  for (const clipboardItem of clipboardItems) {
    const imageTypes = clipboardItem.types.filter(type => type.startsWith('image/'))
    for (const type of imageTypes) {
      const blob = await clipboardItem.getType(type)
      const fileName = `clipboard-image-${Date.now()}.${type.split('/')[1]}`
      const file = new File([blob], fileName, {
        type,
        lastModified: Date.now(),
      })
      imageFiles.push(file)
    }
  }

  if (imageFiles.length) {
    uploadManually(imageFiles)
  }
})

pluginAgent.onSummaryLinkFn((link) => {
  emits('update:modelValue', `${link}

深度总结以上链接`)
  emits('enter')
})

pluginAgent.onReciveBase64((base64) => {
  uploadManually([base64ToFile(base64)])
})

watch(() => focus.value, (val) => {
  if (val) {
    requestPlugin({ action: 'CelHive Ready' })
  }
})
</script>

<style lang="scss" scoped>
// 不要滚动条
::-webkit-scrollbar {
  display: none;
}

.send-box {
  @apply min-h-52px bg-[var(--send-box-bg)] border-1px border-solid border-[var(--send-box-border)] border-rd-31px p-0 z-99 lt-md:(w-90% min-h-36px);

  width: v-bind(sendboxwidth);

  .text-main::placeholder {
    color: var(--send-box-placeholder);
  }

  .suggestion {
    color: rgba(0, 0, 0, 30%);
  }
}

.send-box--loading {
  background: transparent;
  box-shadow: none;
  height: 52px;
  width: calc(100% - 60px);
  border-color: transparent;
}

.send-box__suffix {
  position: absolute;
  right: 17px;
  font-size: 24px;
  z-index: 2;

  @apply lt-md:(right-8px);
}

.send-box__container {
  border-radius: 30px;
  overflow: hidden;
  height: 52px;
  width: 100%;
  position: relative;
}

.send-box__process {
  height: 52px;
  width: 0;
  border-radius: 30px;
  background: #ffbb6a;
  transition: width 10s var(--ani-bezier);
}

.send-box__process--recording::after {
  content: "" !important;
}

.send-box__process::after {
  content: attr("title");
  position: absolute;
  color: #a8a8a8;
  left: 30px;
  line-height: 52px;
  font-size: 14px;
}

.send-box__control {
  position: absolute;
  top: -50px;
  left: 0;
}

.send-box__control > div {
  height: 30px;

  // background: var(--send-box-resend-button);
  box-shadow: 0 5px 20px 0 #00000012;
  border-radius: 5px;
  padding: 0 8px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-box__control .dots {
  animation: dot-animation 0.3s infinite alternate;
  animation-delay: 0.3s;
}

.send-box__control .dots::before,
.send-box__control .dots::after {
  content: ".";
  opacity: 0;
  animation: dot-animation 0.3s infinite alternate;
}

.send-box__control .dots::before {
  animation-delay: 0.2s;
}

.send-box__control .dots::after {
  animation-delay: 0.3s;
}

@keyframes dot-animation {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.cus-transition-enter-from,
.cus-transition-leave-to {
  opacity: 0;
  transform: translateY(30px);
}

.cus-transition-enter-to,
.cus-transition-leave-from {
  opacity: 1;
  transform: translateY(0);
}

.cus-transition-enter-active,
.cus-transition-leave-active {
  transition: all 0.3s var(--ani-bezier);
}

.suggestion-button {
  @apply flex-c border-1px border-[#E7E9ED] border-solid rounded-6px text-#CDCDCD shadow-[0_2px_5px_0_#0000000f] dark:(border-#272727 bg-#363636 text-#727272);
}

.send_btn {
  border: 1px solid #0000 !important;

  &.is-disabled {
    @apply bg-[var(--send-button-disabled-bg)];
  }
}

.suggestion::-webkit-scrollbar {
  display: none;
}

.suggestion {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}
</style>
