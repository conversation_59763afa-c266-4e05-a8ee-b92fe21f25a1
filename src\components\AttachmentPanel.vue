<template>
  <ResizablePanel
    v-model:visible="visible"
    v-model:full-visible="fullVisible"
    v-model:active-tab="tab"
    :tabs="panelTabs"
    :is-share-page="false"
    background-color="#F9FAFC"
    :full-screenable="false"
  >
    <template #content>
      <section v-if="tab === 'list'" class="px-50px py-47px">
        <div class="mb-16px text-24px text-#18181a font-450 font-Lexend">Content</div>

        <section class="space-y-16px">
          <AttachmentCard
            v-for="(item, index) in currentChatAttachment"
            :key="index"
            class="cursor-pointer"
            :file-id="item?.fileId"
            :file-name="item?.fileName"
            :show-type="true"
            :only-display="true"
            @click="handlePreview(item)"
          >
          </AttachmentCard>
        </section>
      </section>

      <section v-else class="h-full flex flex-col overflow-auto">
        <div
          v-if="fileShowType !== FILE_TYPE.PDF"
          class="px-30px pb-20px pt-26px text-24px text-#18181a font-450 font-Lexend"
        >
          {{ currentPreviewFile.fileName }}
        </div>
        <div v-if="manualFetchContent" class="p-30px pb-100px">
          {{ manualFetchedContent }}
        </div>
        <iframe
          v-else
          :src="previewUrl"
          frameborder="0"
          class="size-full"
        ></iframe>
      </section>
    </template>
  </ResizablePanel>
</template>

<script setup lang="ts">
import service from '@/common/axios'
import { getFileShowType } from '@/common/tools'
import { FILE_TYPE } from '@/model/common'
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()
const {
  currentChatAttachment,
} = storeToRefs(appStore)
const visible = defineModel('visible', { type: Boolean, default: false })
const fullVisible = ref(false)
const tab = defineModel<'list' | 'preview'>('tab', {
  default: 'list',
})
const panelTabs = [
  { key: 'list', label: 'Files' },
  { key: 'preview', label: 'Preview' },
]

const previewUrl = ref('')
const currentPreviewFile = ref<any>(null)
const manualFetchedContent = ref('')
const fileShowType = computed(() => {
  return getFileShowType(currentPreviewFile.value.fileName)
})

const isOffice = computed(() => {
  return (
    fileShowType.value === FILE_TYPE.WORD
    || fileShowType.value === FILE_TYPE.PPT
    || fileShowType.value === FILE_TYPE.EXCEL
  )
})

async function handlePreview(item: any) {
  currentPreviewFile.value = item
  await getFileLink(item)
  tab.value = 'preview'
}

async function previewFile(file: any) {
  currentPreviewFile.value = file
  await getFileLink(file)
  tab.value = 'preview'
}
const manualFetchContent = computed(() => {
  if (isOffice.value || fileShowType.value === FILE_TYPE.PDF) {
    return false
  }
  return true
})

async function getFileLink(file: any) {
  const url = `${import.meta.env.VITE_APP_V1_API}/v1/app/file/getLink`
  const res = await service.post<any, { fileLink: string }>(
    url,
    { fileId: file.fileId },
    {
      headers: { noninductive: true },
    },
  )

  // 处理office文件
  if (isOffice.value) {
    previewUrl.value = `https://view.officeapps.live.com/op/view.aspx?src=${res.fileLink}`
  }
  else {
    previewUrl.value = res.fileLink
    if (fileShowType.value !== FILE_TYPE.PDF) {
      fetch(previewUrl.value).then(res => res.text()).then((text) => {
        manualFetchedContent.value = text
      })
    }
  }
}

// 暴露方法给外部组件
defineExpose({
  previewFile,
})
</script>

<style lang="scss" scoped>
</style>
