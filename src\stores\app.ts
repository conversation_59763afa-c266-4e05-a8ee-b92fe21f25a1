import type { ElUpload, UploadRawFile } from 'element-plus'
import type { AttachmentDataModel, AttachmentUploadRes } from '@/model/attachmentModel'
import type { OSSImageModel } from '@/model/common'
import type { ModeItemModel } from '@/model/modes'
import type { EUploadUserFile } from '@/types'
import type { Section, SectionType } from '@/types/index'
import { nanoid } from 'nanoid'
import { modeTransSelected } from '@/common'
import { accepts, ATTACHMENTS_NUMBER_LIMIT, DefaultModelId } from '@/common/constant'
import { formatNumberToK, getFileCategory } from '@/common/tools'
import { cssBreakPoints } from '@/config/cssBreakPoints'
import { Model } from '@/config/model'
import { providerAppIconMap } from '@/config/providerIcons'
import { GENERATE_IMAGE_MODES, SCAN_IMAGE_MODES } from '@/enum'
import { FILE_CATEGORY } from '@/model/common'
import { mixpanel } from '../config/mixpanel'

const SUPPORTED_THINKING_MODEL_IDS = [
  Model.CLAUDE35_SONNET,
  Model.CLAUDE37_SONNET,
  Model.CLAUDE4_SONNET,
  Model.CLAUDE4_OPUS,
  Model.DEEPSEEK_R1,
  Model.GPT_O1_PREVIEW,
  Model.GPT_O3_MINI,
  Model.ZHIPU_GLM_4_5,
  Model.GPT_OSS_120B,
  Model.GPT_5_MINI,
  Model.GPT_5_NANO,
  Model.GPT_5,
]

export const useAppStore = defineStore(
  'app',
  () => {
    const isZenMode = ref(false) // 是否开启禅模式
    const isZenModeDelay = ref(false) // 带延迟的禅模式状态，等isZenMode为false后，继续为true一段时间，用于控制一些动画效果
    const artifactsHtmlVisible = ref(false) // Artifacts HTML面板是否可见
    const currentArtifacts = ref<{
      id: number
      title: string
      description: string
      html: string
    }>() // 当前选中的Artifacts
    const breakpoints = useBreakpoints(cssBreakPoints)
    const isPc = breakpoints.greaterOrEqual('md') // md及以上算PC
    const removeFileEvent = new Set<(uid: number) => void>() // 订阅删除文件的事件
    const headerBarWidth = ref(0) // HeaderBar2的宽度
    const isResizablePanelVisible = ref(false) // ResizablePanel是否可见
    const activeResendModelId = ref<number | null>(null) // 当前正在用于“重新回答”操作的模型 ID
    const collapseCodeCounter = ref(nanoid()) // 用于触发代码块折叠/展开的计数器

    // 环境变量检查
    const isNotProduction = computed(() => {
      return import.meta.env.MODE !== 'production'
    })

    const languageTypeDefault = ref(-1)
    // v2版本接口模型
    const modelData = ref<any>()
    // 选择的模型信息
    const storedMode = useStorage('selected-model-mode', DefaultModelId)
    const modelSelectInfo = ref({
      mode: 0,
      ttsId: 0,
      languageId: 0,
      languageType: languageTypeDefault.value,
      serviceProviderName: '',
      modelName: '',
      providerNameIcon: '',
      selected: 0,
      enums: [
        { label: '文字 → 语音', id: 1 },
        { label: '语音 → 语音', id: 2 },
        { label: '语音 → 文字', id: 3 },
      ],
      types: [],
      models: [],
      maxToken: '0',
    })

    // 监听mode的变化并同步到localStorage
    watch(
      () => modelSelectInfo.value.mode,
      (newMode) => {
        if (newMode === 0) {
          return
        }
        storedMode.value = newMode
      },
      { deep: true },
    )

    // 当前选择的模型
    const selectedMode = computed<ModeItemModel | undefined>(() => {
      const models = modelSelectInfo.value.models
      const mode = modelSelectInfo.value.mode
      for (let i = 0; i < models.length; i++) {
        const item = models[i]
        for (let j = 0; j < item.modes.length; j++) {
          const innerItem = item.modes[j]
          if (mode === innerItem.id) { return innerItem }
        }
      }
    })

    const userAgent = window.navigator.userAgent.toLowerCase()

    // 是否显示附件按钮
    const showAttachment = computed(() => {
      return true
    })

    // 虽然上传按钮一直会显示，但是有些模型是不能上传附件的
    const canUploadAttachment = computed(() => {
      return selectedMode.value ? SCAN_IMAGE_MODES.includes(selectedMode.value?.type) : false
    })

    // 是否为客户端
    const isClient = computed(() => window.pakeToast)

    // 检测是否是 iOS 设备
    const isIOS = computed(() => {
      const isIPadOS = navigator.maxTouchPoints > 1 && /macintosh/.test(userAgent)
      return /iphone|ipod/.test(userAgent)
        || /ipad/.test(userAgent)
        || isIPadOS
    })

    // 检测是否是 Mac 设备.
    const isMac = computed(() => {
      return /macintosh|mac os x/.test(userAgent)
        && !isIOS.value
        && navigator.maxTouchPoints <= 1
    })

    // 是否为苹果系统
    const isApple = computed(() => {
      return isIOS.value || isMac.value
    })

    // 是否是生成图像模型
    const isGenerateImage = computed(() =>
      selectedMode.value ? GENERATE_IMAGE_MODES.includes(selectedMode.value?.type) : false,
    )

    // 上传附件数据
    const attachments = ref<EUploadUserFile[]>([])
    const attachmentsPreviewList = computed(() => {
      return [
        ...attachments.value.filter((item) => {
          return getFileCategory(item.name) === FILE_CATEGORY.IMAGE
        }),
        ...attachments.value.filter((item) => {
          return getFileCategory(item.name) !== FILE_CATEGORY.IMAGE
        }),
      ]
    })
    const attachmentAbortControllers = ref<Map<string, AbortController>>(new Map())

    // 往附件列表中添加附件的解析状态，会添加一个 analyzeStatus 字段
    function updateAttachmentParseStatus(fileId: string, success: boolean) {
      const index = attachments.value.findIndex(
        attachment => attachment?.response?.data?.fileId === fileId,
      )

      if (index !== -1) {
        attachments.value[index] = {
          ...attachments.value[index],
          analyzeStatus: success ? 'success' : 'failed',
          isParsing: false,
        }
      }
    }
    // 根据 uid 删除附件
    function removeAttachmentByFileUid(uid?: number) {
      if (uid) {
        const attachment: any = attachments.value.find((item) => {
          return item.uid === uid
        })
        if (attachment) {
          const fileId = attachment.response?.data?.fileId
          if (fileId) {
            const controller = attachmentAbortControllers.value.get(fileId)
            if (controller) {
              controller.abort() // 取消相关的解析请求
              attachmentAbortControllers.value.delete(fileId)
            }
          }
          attachments.value = attachments.value.filter((item) => {
            const result = item.uid !== uid
            if (!result) {
              removeFileEvent.forEach((callback) => {
                callback(uid)
              })
            }
            return result
          })
        }
      }
    }

    // 上传附件结果
    const attachmentsRes = computed(() => {
      const files: {
        [FILE_CATEGORY.IMAGE]: OSSImageModel[]
        [FILE_CATEGORY.TEXT]: AttachmentDataModel[]
        [FILE_CATEGORY.DATA]: AttachmentDataModel[]
        [FILE_CATEGORY.AUDIO]: AttachmentDataModel[]
      } = {
        [FILE_CATEGORY.IMAGE]: [],
        [FILE_CATEGORY.TEXT]: [],
        [FILE_CATEGORY.DATA]: [],
        [FILE_CATEGORY.AUDIO]: [],
      }
      attachmentsPreviewList.value.forEach((item) => {
        const response: any = item.response
        if (item.status === 'success' && response && response.success) {
          const category = getFileCategory(item.name)
          const responseData = response.data
          switch (category) {
            case FILE_CATEGORY.IMAGE:
            {
              const resData: OSSImageModel = responseData
              files[category].push(resData)
              break
            }
            case FILE_CATEGORY.DATA:
            case FILE_CATEGORY.TEXT:
            case FILE_CATEGORY.AUDIO:
            {
              const resData: AttachmentUploadRes = responseData
              files[category].push({
                ...resData,
                fileType: category,
              })
              break
            }
          }
        }
      })
      return files
    })

    // uploader instance
    const uploader = ref<InstanceType<typeof ElUpload>>()

    // 删除所有文件
    function removeAllFiles() {
      attachments.value = []
    }

    // upload manually
    function uploadManually(files: File[]) {
      const diff = ATTACHMENTS_NUMBER_LIMIT - attachments.value.length
      if (files.length > diff) {
        ElNotification.error({
          message: `最多只能同时上传${ATTACHMENTS_NUMBER_LIMIT}个附件！`,
        })
        files = files.slice(0, diff)
      }
      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        if (accepts.includes(file.type)) {
          uploader.value?.handleStart(file as UploadRawFile)
        }
      }
      uploader.value?.submit()
    }

    // 聊天视图数据
    const chatViewData = ref({
      messageId: 0,
      messageName: '',
      groups: [],
      sections: [] as Section[],
      groupId: undefined,
      chatContent: undefined,
      status: '',
    })

    const currentChatAttachment = ref<any[]>([])

    // 重置选择的模型信息
    function resetModel() {
      modelSelectInfo.value.languageType = languageTypeDefault.value
      modelSelectInfo.value.languageId = 0
      modelSelectInfo.value.mode = Math.min(
        ...Object.values(modelData.value)
          .flat()
          .map((item: any) => Number.parseInt(item.id)),
      )
      modelSelectInfo.value.ttsId = 0
    }

    // **********删除消息 Start**********
    // 删除消息触发器
    const deleteMessageTrigger = ref({
      count: 0, // 用于触发响应的计数器
      id: undefined, // 要删除的消息ID
    })

    // 发送删除消息
    function sendDeleteMessage(id: any) {
      deleteMessageTrigger.value = {
        count: deleteMessageTrigger.value.count + 1,
        id,
      }
    }
    // **********删除消息 End**********

    // ----------星标切换 Start----------
    // 星标切换触发器
    const favoriteMessageTrigger = ref({
      count: 0,
      id: undefined,
    })

    // 发送星标切换
    function sendFavoriteMessage(id: any) {
      favoriteMessageTrigger.value = {
        count: favoriteMessageTrigger.value.count + 1,
        id,
      }
    }
    // ----------星标切换 End----------

    // ==========移动分组 Start==========
    // 移动分组触发器
    const moveGroupTrigger = ref({
      count: 0,
      id: undefined,
    })

    // 发送移动分组
    function sendMoveGroup(id: any) {
      moveGroupTrigger.value = {
        count: moveGroupTrigger.value.count + 1,
        id,
      }
    }
    // ==========移动分组 End==========

    // 操作ControlBar的选择触发器
    const controlBarSelectTrigger = ref({
      count: 0,
      index: -1,
    })

    // 发送ControlBar的选择
    function sendControlBarSelect(index: number) {
      controlBarSelectTrigger.value = {
        count: controlBarSelectTrigger.value.count + 1,
        index,
      }
    }

    const expandSidebar = ref(!!isPc.value)

    // 可以导出的选项
    const exportOptions = ref([
      { type: 'PDF', label: 'PDF', width: '50px' },
      { type: 'PNG', label: 'PNG', width: '50px' },
      { type: 'TXT', label: 'TXT', width: '50px' },
      { type: 'WORD', label: 'Word', width: '50px' },
      { type: 'EXCEL', label: 'Excel', width: '50px' },
      { type: 'MARKDOWN', label: 'Markdown', width: '90px' },
    ])

    // 模型分组 未检索到调用，已作废
    // const splitModels: any = computed(() => {
    //   if (!modelSelectInfo.value.models?.length) { return {} }
    //   const array = modelSelectInfo.value.models.flatMap((element: any) => element.modes)

    //   const object: any = {
    //     OPENAI: [],
    //     ANTHROPIC: [],
    //     // MIXTRAL: [],
    //     GEMINI: [],
    //     VISION: [],
    //     LABS: [],
    //   }
    //   array.forEach((element: any) => {
    //     if ([11, 17, 21, 26, 27].includes(element.id)) {
    //       object.OPENAI.push(element)
    //     }
    //     if ([5, 6, 20, 30].includes(element.id)) {
    //       object.ANTHROPIC.push(element)
    //     }
    //     if ([14, 16].includes(element.id)) {
    //       object.GEMINI.push(element)
    //     }
    //     if ([24, 25, 19, 12].includes(element.id)) {
    //       object.VISION.push(element)
    //     }
    //     if ([15, 28, 26].includes(element.id)) {
    //       object.LABS.push(element)
    //     }
    //     // if ([5].includes(element.type)) {
    //     //   object.MIXTRAL.push(element)
    //     // }
    //     // else if ([6, 12, 7].includes(element.type)) {
    //     //   object.ANTHROPIC.push(element)
    //     // }
    //     // else if ([0, 1, 2].includes(element.type)) {

    //     // }
    //     // else {
    //     //   object.LABS.push(element)
    //     // }
    //   })
    //   return {
    //     ...object,
    //     OPENAI: object.OPENAI
    //       .sort((a: any) => (a.type === 2 ? -1 : 0))
    //       .sort((a: any) => (a.type === 0 ? -1 : 0)),
    //   }
    // })

    // 根据模型id获取模型的showName
    function getModelShowNameById(groupModelData: any, id: number) {
      for (const modelGroupKey in groupModelData) {
        const modelsArray = groupModelData[modelGroupKey]

        // 遍历每个模型数组
        for (const model of modelsArray) {
          // 检查模型的id是否匹配传入的id
          if (model.id === id) {
            // 如果找到匹配的id,返回模型的 服务商 和 showName
            return [modelGroupKey, model.showName]
          }
        }
      }
      // 如果没有找到匹配的id,返回空数组
      return []
    }

    function getModelNameById(id: number) {
      const model = modelSelectInfo.value.models.reduce((result, item: { modes: any[] }) => {
        return result.concat(item.modes)
      }, [] as any[]).find((item: any) => item.id === id)
      return model ? model.showName : ''
    }

    function getModelMaxTokenById(groupModelData: any, id: number): string {
      if (!groupModelData) { return '0' }

      // 获取所有模型类别的数组
      const allCategories: any[] = Object.values(groupModelData)

      // 遍历所有类别寻找匹配的模型
      for (const category of allCategories) {
        const model = category.find((model: any) => model.id === id)
        if (model) {
          return formatNumberToK(model.maxToken)
        }
      }

      // 如果没有找到匹配的模型，返回 null
      return '0'
    }

    // 当前语种下的朗读角色列表
    const currentReadRoleList = computed(() => {
      return modelSelectInfo.value.types[modelSelectInfo.value.languageType]?.detailVos
    })

    const isArtifact = computed(() => {
      return modelSelectInfo.value.mode === 30
    })

    // 返回由languageType、ttsId、languageId的汉字名称组成的数组
    function getLanguageString() {
      const language = modelSelectInfo.value.types[modelSelectInfo.value.languageType]?.lang
      const tts = modelSelectInfo.value.enums.find((item: any) => item.id === modelSelectInfo.value.ttsId)?.label
      const role = currentReadRoleList.value?.find((item: any) => item.id === modelSelectInfo.value.languageId)?.name || ''
      return [language, tts, role]
    }

    // 供应商与图标的映射关系
    const providerNameIconMap: any = providerAppIconMap

    // 用来更新当前model的 服务商名/模型名/服务商图标
    async function updateModelInfo() {
      const serviceProviderName = modelSelectInfo.value.mode === 0
        ? 'Voice'
        : getModelShowNameById(modelData.value, modelSelectInfo.value.mode)?.[0] || ''

      const maxToken = getModelMaxTokenById(modelData.value, modelSelectInfo.value.mode)
      modelSelectInfo.value = Object.assign({}, modelSelectInfo.value, {
        serviceProviderName,
        maxToken,
        modelName: modelSelectInfo.value.mode === 0
          ? getLanguageString()
          : getModelShowNameById(modelData.value, modelSelectInfo.value.mode)?.[1] || '',
        providerNameIcon: providerNameIconMap?.[serviceProviderName] || '',
      })
    }

    // 监听各种id的变化,更新模型的 服务商名/模型名/服务商图标
    watch(
      [
        () => modelSelectInfo.value.mode,
        () => modelSelectInfo.value.languageType,
        () => modelSelectInfo.value.languageId,
        () => modelSelectInfo.value.ttsId,
      ],
      updateModelInfo,
    )

    // 监听语种变化会自动重置选择面板的内容
    // 自动重置逻辑: 会重置3个东西
    // 模型
    // 文字 → 语音
    // 朗读角色
    function autoResetVoiceParams() {
      if (![0, undefined, languageTypeDefault.value].includes(modelSelectInfo.value.languageType)) {
        modelSelectInfo.value.mode = 0 // 模型选择置空
      }

      // 「文字 → 语音」如果没有选中，且没有选中模型 的话,自动选中第一个
      if (!modelSelectInfo.value.ttsId && !modelSelectInfo.value.mode) {
        modelSelectInfo.value.ttsId = modelSelectInfo.value.enums?.[0].id
      }

      // 如果朗读角色没有选中 或者 当前选中的朗读角色不属于这个语言,自动选中恢复第一个
      if (!modelSelectInfo.value.languageId || currentReadRoleList.value?.find((item: any) => item.id === modelSelectInfo.value.languageId) === undefined) {
        modelSelectInfo.value.languageId = currentReadRoleList.value?.[0].id
      }
    }

    // 监听语种变化,自动重置「文字 → 语音」/朗读角色/模型
    watch(() => modelSelectInfo.value.languageType, autoResetVoiceParams)

    // 监听Pinia里面的模型变化，自动修改selected的值，为了SendBox的形态切换
    watch(
      [
        () => modelSelectInfo.value.mode,
        () => modelSelectInfo.value.languageType,
        () => modelSelectInfo.value.languageId,
        () => modelSelectInfo.value.ttsId,
      ],
      () => {
        modelSelectInfo.value.selected = modeTransSelected(modelSelectInfo.value.mode, modelSelectInfo.value.models)
      },
    )

    // 选择朗读角色
    const selectLanguageId = (id: number) => {
      modelSelectInfo.value.languageId = id
    }

    // 选择阅读语种
    const selectLanguage = (index: number) => {
      modelSelectInfo.value.languageType = index // 高亮用的数据
      modelSelectInfo.value.mode = 0 // 模型选择置空
    }

    const selectTts = (id: number) => {
      modelSelectInfo.value.ttsId = id
    }

    // 选择LLM模型
    function selectMode(id: number) {
      selectLanguage(languageTypeDefault.value) // 语种重置为默认值
      selectTts(0) // 「文字 → 语音」重置为空值
      modelSelectInfo.value.mode = id
      triggerRestart()
      triggerNewMessage()

      nextTick(() => {
        mixpanel.selectModel(modelSelectInfo.value.modelName)
      })
    }

    // 用于触发断开LLM输出
    const restartTrigger = ref(0)
    // 用于触发新建对话
    const newMessageTrigger = ref(0)
    function triggerRestart() {
      restartTrigger.value++
    }

    function triggerNewMessage() {
      newMessageTrigger.value++
    }

    /** 检查 sections 中是否存在支持 thinking 功能的模型 */
    const hasThinkingModelInSections = computed(() => {
      const checkSections = (sections: Section[]): boolean => {
        if (!sections || sections.length === 0) {
          return false
        }
        for (const section of sections) {
          if (section.modeId && SUPPORTED_THINKING_MODEL_IDS.includes(section.modeId)) {
            return true
          }
          if (section.children && section.children.length > 0) {
            if (checkSections(section.children as Section[])) {
              return true
            }
          }
        }
        return false
      }
      return checkSections(chatViewData.value.sections)
    })

    /** 开启「Thinking」功能样式的模型 */
    const thinkingTimeFunctional = computed(() => {
      const isSelectedModelSupported = selectedMode.value
        ? SUPPORTED_THINKING_MODEL_IDS.includes(selectedMode.value.id)
        : false

      // 「重新回答」的模型是否支持「Thinking」功能
      const isActiveResendModelSupported = activeResendModelId.value !== null
        ? SUPPORTED_THINKING_MODEL_IDS.includes(activeResendModelId.value)
        : false

      return isSelectedModelSupported || hasThinkingModelInSections.value || isActiveResendModelSupported
    })

    /** 隐藏「Thinking」功能样式的模型 */
    const hiddenThinkingTimeFunctional = computed(() => {
      return false
      // if (!selectedMode.value) { return false }
      // return [
      //   Model.CLAUDE4_SONNET,
      // ].includes(selectedMode.value.id)
    })

    const { enter, exit } = useFullscreen()
    watch(isZenMode, (newVal) => {
      if (newVal) {
        isZenModeDelay.value = true
        enter()
      }
      else {
        const timer = setTimeout(() => {
          isZenModeDelay.value = false
          clearTimeout(timer)
        }, 600)
        exit()
      }
    })

    function subscribeRemoveFileEvent(callback: (uid: number) => void) {
      if (typeof callback !== 'function') {
        throw new TypeError('callback must be a function')
      }
      if (removeFileEvent.has(callback)) {
        removeFileEvent.delete(callback)
      }
      removeFileEvent.add(callback)
      return () => {
        removeFileEvent.delete(callback)
      }
    }

    // 套餐升级触发器
    const upgradePackageTrigger = ref({
      count: 0,
    })

    // 触发套餐升级
    // index.vue中监听了这个数值，当数值变化时，会打开套餐升级页面
    function triggerUpgradePackage() {
      upgradePackageTrigger.value.count++
    }

    const chatViewSectionWidth = ref(0)

    // 用于设置「重新回答」的模型 ID
    function setActiveResendModelId(modelId: number | null) {
      activeResendModelId.value = modelId
    }

    // 执行这个即可触发给ChatContent里面的pre添加code-height-max类名，实现代码块折叠
    function triggerCodeCollapse() {
      nextTick(() => {
        collapseCodeCounter.value = nanoid()
      })
    }

    return {
      isPc,
      isClient,
      isApple,
      isIOS,
      modelSelectInfo,
      chatViewData,
      deleteMessageTrigger,
      sendDeleteMessage,
      favoriteMessageTrigger,
      sendFavoriteMessage,
      moveGroupTrigger,
      sendMoveGroup,
      controlBarSelectTrigger,
      sendControlBarSelect,
      expandSidebar,
      languageTypeDefault,
      resetModel,
      exportOptions,
      selectedMode,
      thinkingTimeFunctional,
      hiddenThinkingTimeFunctional,
      showAttachment,
      canUploadAttachment,
      isGenerateImage,
      attachments,
      attachmentsPreviewList,
      attachmentsRes,
      removeAttachmentByFileUid,
      removeAllFiles,
      uploadManually,
      uploader,
      attachmentAbortControllers,
      updateAttachmentParseStatus,
      // splitModels,
      currentReadRoleList,
      selectLanguageId,
      selectLanguage,
      selectTts,
      selectMode,
      restartTrigger,
      newMessageTrigger,
      triggerRestart,
      triggerNewMessage,
      breakpoints,
      isArtifact,
      modelData,
      isZenMode,
      isZenModeDelay,
      subscribeRemoveFileEvent,
      artifactsHtmlVisible,
      currentArtifacts,
      isNotProduction,
      upgradePackageTrigger,
      triggerUpgradePackage,
      currentChatAttachment,
      chatViewSectionWidth,
      getModelNameById,
      headerBarWidth,
      isResizablePanelVisible,
      activeResendModelId,
      setActiveResendModelId,
      collapseCodeCounter,
      triggerCodeCollapse,
    }
  },
)
