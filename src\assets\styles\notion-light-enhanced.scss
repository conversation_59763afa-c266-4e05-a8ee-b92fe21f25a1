// Notion样式，不处理
.chat-view__content {
  --bg-color: #fff;
  --text-color: var(--chat-text-color);
  --body-text-color: var(--chat-text-color);
  --text-cursor-color: var(--text-color);
  --secondary-text-color: #73726e;
  --text-accent-color: #e9e9e7;
  --bg-color-dark: #f7f6f3;
  --text-highlight-color: #4f3b2a;
  --text-highlight-bg: #feecc8;
  --code-fence-text-color: #37352f;
  --code-fence-bg-color: #f7f6f3;
  --blockquote-bg-color: #f7f7f7;
  --blockquote-accent-color: var(--text-color);
  --blockquote-text-color: var(--text-color);
  --light-trait-100: #38352f;
  --light-trait-200: #37352f;
  --light-trait-300: #37352f;
  --light-trait-400: #37352f;
  --todo-bg-color: #2eaadc;
  --todo-tick-color: #fff;
  --search-select-bg-color: #edf3ec;
  --search-select-text-color: #448361;
  --search-hit-text-color: #d44c47;
  --search-hit-bg-color: #fdebec;
  --url-text-color: var(--chat-link-color);
  --url-underline-color: #37352f;
  --footnote-text-color: #f7f6f3;
  --footnote-bg-color: #888884;
  --table-primary-color: var(--p5);
  --table-secondary-color: #fdfdfd;
  --select-text-bg-color: #c0e5f4;
  --code-color: #9a6e3a;
  --code-cursor-color: var(--text-color);
  --kbd-text-color: #73726e;
  --kbd-bg-color: var(--bg-color-dark);

  /* Menu system */
  --side-bar-bg-color: var(--bg-color-dark);
  --side-bar-text-color: var(--text-color);
  --item-hover-bg-color: #e8e7e4;
  --window-border: 1px solid var(--bg-color);
  --active-file-bg-color: #e9e7e4;
  --active-file-border-color: var(--active-file-bg-color);
  --active-file-text-color: var(--text-color);
  --footer-bg-color: var(--side-bar-bg-color);
  --control-text-color: #72706b;

  /* General */
  --font-size: 16px;
  --monospace: menlo, monaco, 'JetBrains Mono Variable', consolas, "Liberation Mono", "Courier New", var(--font-family);
  --heading-char-color: var(--light-trait-400);
  --color-popover-bg-color: var(--text-color);
  --rawblock-edit-panel-bd: var(--bg-color-dark);
  --meta-content-color: var(--body-text-color);
  --primary-btn-border-color: var(--body-text-color);
  --border-radius: 4px;

  /* Code variables */
  --cm-line: #999;
  --cm-variable: #37352f;
  --cm-keyword: #0277aa;
  --cm-tag: #ff5a5a;
  --cm-bracket: #ff5a5a;
  --cm-error: #ff5a5a;
  --cm-attribute: #0277aa;
  --cm-def: #dc4a68;
  --cm-comment: #708090;
  --cm-string: #690;
  --cm-operator: #9a6e3b;
  --cm-number: #905;
  --cm-meta: var(--text-color);
  --cm-atom: #845dc4;
  --cm-builtin: #690;
  --cm-property: var(--text-color);
  --cm-variable-2: var(--text-color);
  --cm-variable-3: #0277aa;
  --cm-gutter: #f1f3f450;

  font-family: var(--font-family);
  -webkit-font-smoothing: antialiased;
  color: var(--body-text-color);
  line-height: 1.6;

  @apply lt-md:(max-w-100vw);

  .chat-view__markdown {
    max-width: 100%;
    overflow: visible;

    > div {
      > p:first-of-type {
        margin-top: 0;
      }

      > div {
        > p:first-of-type {
          margin-top: 0;
        }
      }
    }
  }

  pre.hljs {
    --code-title-height: 41px;

    @apply animate-name-fadeIn animate-duration-0.6s;

    border-radius: 10px;
    overflow: auto;
    padding: 1rem;
    padding-top: calc(1rem + var(--code-title-height));
    position: relative;
    max-width: 100%;
    width: 100%;
    border: 1px solid var(--network-card-border);
    margin: 1.4em 0;

    @include narrow {
      min-width: 100%;
    }

    .code-title {
      border-bottom: 1px solid var(--network-card-border);
      background: var(--network-card-bg);
      position: absolute;
      width: 100%;
      left: 0;
      top: 0;

      // padding: 1em;padding
      display: flex;
      height: var(--code-title-height);
      padding: 0 0 0 16px;
      font-family: Inter, serif;

      .lang-text {
        text-transform: capitalize;
        user-select: none;
        height: 100%;
      }

      .hljs__action {
        line-height: 0;
        position: absolute;
        top: 0;
        right: 3px;
        height: 100%;
        width: fit-content;
        display: flex;
        align-items: center;

        .copy-btn {
          transition: all 0.3s var(--ani-bezier);
          padding: 0.2em 0;
          width: 34px;
          height: 34px;
          display: flex;
          justify-content: center;
          border-radius: 8px;
          min-height: 32px;
          color: #4c4c4c;
          user-select: none;
          font-size: 13px;

          > * {
            pointer-events: none;
          }

          &:hover svg g {
            stroke: black;
          }

          &.copied {
            color: #00ab3c;
            background: #d7ffd4;
            pointer-events: none;
          }
        }

        .copy-icon {
          width: 20px;
        }

        .render-mermaid {
          transition: 0.3s;
          padding: 0.2em 0;
          width: 34px;
          height: 34px;
          display: flex;
          justify-content: center;
          border-radius: 8px;
          min-height: 32px;
          color: #4c4c4c;
          user-select: none;
          font-size: 13px;

          > * {
            pointer-events: none;
          }

          &:hover svg g {
            stroke: black;
          }

          &.copied {
            color: #00ab3c;
            background: #d7ffd4;
            pointer-events: none;
          }

          .render-mermaid-icon {
            svg:last-child {
              display: none;
            }
          }

          &.rendered {
            svg:first-child {
              display: none;
            }

            svg:last-child {
              display: block;
            }
          }
        }

        .toggle-wrap-btn {
          transition: 0.3s;
          padding: 0.2em 0;
          width: 34px;
          height: 34px;
          display: flex;
          aspect-ratio: 1/1;
          justify-content: center;
          border-radius: 8px;
          min-height: 32px;
          color: #4c4c4c;
          user-select: none;
          font-size: 13px;

          > * {
            pointer-events: none;
          }

          .toggle-wrap-icon:not(.active) svg {
            color: var(--d3);
          }
        }
      }
    }

    code {
      padding: 0 !important;
      white-space: break-spaces;
      word-break: break-word;
      font-size: 13px;
      line-height: 1.5;
      overflow-x: visible;
    }

    .expand-and-collapse {
      width: 26px;
      height: 100%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;

      svg {
        width: 8px;
        height: 8px;
      }
    }
  }


  
  p, li {
    > code {
      color: #000 !important;
      background: #ff880038 !important;
      margin: 2px;
      border-radius: 5px;

      @apply dark:(bg-[#929292]! text-[#1C1C1C]!);

    }
  }

  code {
    padding: 2px;
  }

  & > ul:first-child,
  & > ol:first-child {
    margin-top: 30px;
  }

  a:not(.citation) {
    color: var(--url-text-color);
    text-decoration: none;
    border-bottom: 0.05em solid;
    border-color: var(--url-underline-color);
    transition: all 0.1s ease-in;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    position: relative;
    margin-top: 2rem;
    margin-bottom: 1rem;
    font-weight: 700;
    line-height: 1.4;
    cursor: text;
  }

  cursor {
    color: var(--text-color);
  }

  h1:hover a.anchor,
  h2:hover a.anchor,
  h3:hover a.anchor,
  h4:hover a.anchor,
  h5:hover a.anchor,
  h6:hover a.anchor {
    text-decoration: none;
  }

  h1 tt,
  h1 code,
  h2 tt,
  h2 code,
  h3 tt,
  h3 code,
  h4 tt,
  h4 code,
  h5 tt,
  h5 code,
  h6 tt,
  h6 code {
    font-size: inherit;
  }

  h1 {
    padding-bottom: 0.3em;
    font-size: 2.2em;
    line-height: 1.3;
    color: var(--text-color);
  }

  h2 {
    padding-bottom: 0.3em;
    font-size: 1.75em;
    line-height: 1.225;
    color: var(--text-color);
  }

  h3 {
    font-size: 1.4em;
    line-height: 1.43;
    color: var(--text-color);
  }

  h4 {
    font-size: 1.2em;
    color: var(--text-color);
  }

  h5 {
    font-size: 1em;
    color: var(--text-color);
  }

  h6 {
    font-size: 1em;
    color: var(--text-color);
  }

  .katex-display {
    overflow: auto hidden;
    box-sizing: border-box;
    word-break: break-word;
    overflow-wrap: break-word;

    @apply max-w-[calc(100vw-116px)] md:max-w-[calc(100vw-30px)] !ml-0 !md:ml-[-40px];

    * {
      line-height: 3em;
    }

    @include narrow {
      margin-left: -40px;
    }
  }

  .chat-view__content__self {
    p {
      white-space: pre-wrap !important;
    }
  }

  li,
  p,
  blockquote,
  dl,
  table {
    word-break: break-word;
    line-height: 1.4;

    @include narrow {
      font-size: 1em;
    }
  }

  p,
  blockquote,
  dl,
  table {
    margin: 0.5em 0;
    color: var(--text-color);
    line-height: 30px;
  }

  ol {
    list-style-type: decimal;

    & + ul {
      margin-left: 1em;
    }
  }

  ul {
    list-style-type: disc;
  }

  ol,
  ul {
    padding-left: 15px;
    color: var(--text-color);

    // @apply max-w-[200px] overflow-hidden md:max-w-[100%]; md:overflow-visible;

    > li {
      ul,
      ol {
        list-style-type: disc;

        > li {
          ul,
          ol {
            list-style-type: circle;

            > li {
              ul,
              ol {
                list-style-type: square;
              }
            }
          }
        }

        li {
          margin: 0.4em 0;
        }
      }
    }
  }

  hr {
    height: 1px;
    border: none;

    @apply bg-#dfdfdf dark:bg-#666;
  }

  a:hover {
    text-decoration: none;
    opacity: 1;
  }

  li {
    margin: 1em 0;

    & > p {
      margin: 0;
    }
  }


  ul:last-child,
  ol:last-child {
    margin-bottom: 0;
  }

  mark {
    background: var(--search-hit-bg-color);
    border-radius: 4px;
    color: var(--text-highlight-color);
    font-weight: inherit;
    background-color: var(--text-highlight-bg);
    padding: 2px 4px;
    margin-left: 2px;
    margin-right: 2px;
  }

  blockquote {
    color: var(--blockquote-text-color);
    margin-left: 1.75px;
    margin-right: 0;
    border-left: 4px solid var(--blockquote-accent-color);
    padding: 10px 14px 7px 22px;
  }

  blockquote blockquote {
    padding-right: 0;
  }

  /* Alternating color rows in table */
  table tr:nth-child(2n) {
    background-color: var(--table-primary-color);
  }

  table tr:nth-child(2n + 1) {
    background-color: var(--table-secondary-color);

    @apply dark:bg-model-select-bg;
  }

  /* Alternating color rows in table */

  table {
    padding: 0;
    word-break: initial;
  }

  table tr {
    border-top: 1px solid var(--text-accent-color);
    margin: 0;
    padding: 0;
  }

  table tr th {
    font-weight: bold;
    border: 1px solid var(--text-accent-color);
    border-bottom: 0;
    margin: 0;
    padding: 6px 13px;
  }

  table tr td {
    border: 1px solid var(--text-accent-color);
    margin: 0;
    padding: 6px 13px;
  }

  table tr th:first-child,
  table tr td:first-child {
    margin-top: 0;
  }

  table tr th:last-child,
  table tr td:last-child {
    margin-bottom: 0;
  }

  kbd {
    font-size: 0.875rem;
    background: var(--kbd-bg-color);
    border: 1px solid var(--text-accent-color);
    box-shadow: 0 2px 0 var(--text-accent-color);
    color: var(--kbd-text-color);
  }

  .md-fences,
  code,
  tt {
    border: none;

    // color: var(--code-fence-text-color);color
    border-radius: var(--border-radius);
    padding: 2px 4px 0;
    font-size: 0.975em;
    font-family: var(--monospace);
  }

  input[type='checkbox'] {
    position: relative;
    cursor: pointer;
    border: 1px solid #a8afbf;
    border-radius: 3px;
    transition: all 0.2s ease;
    background-color: transparent;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    display: inline-block;
    vertical-align: middle;
    flex-shrink: 0;
  
    &:checked {
      background-color: #f65134;
      border-color: #f65134;

  
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -60%) rotate(45deg);
        width: 4px;
        height: 8px;
        border: solid white;
        border-width: 0 2px 2px 0;
        display: block;
      }
    }
  
    &:hover:not(:checked) {
      border-color: #f65134;
    }
  
    &:disabled {
      opacity: 1;
      cursor: default;
      width: 16px !important;
      height: 16px !important;
      
      &:checked {
        background-color: #f65134;
        border-color: #f65134;
      }
      
      &:not(:checked) {
        border-color: #a8afbf;
      }
    }
  }
  
  .checkbox {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    user-select: none;
  }
}


.thinking-time-functional {
  .juchats-thinking,
  juchats-thinking {
    @apply block! relative font-Inter text-13px;
  }

  .juchats-thinking,
  juchats-thinking,
  pre.hljs:has(.language-thinking) {
    @apply border-none m-0  bg-[var(--artifacts-code-path-bg)] rounded-10px p-16px mb-16px mt-[64px] overflow-visible;

    code {
      @apply font-Inter text-13px;
    }
  }

  .juchats-code-wrapper * {
    @apply transition-none! visible!
  }

  .juchats-thinking:has(.collapse-thinking),
  juchats-thinking:has(.collapse-thinking),
  pre.hljs:has(.collapse-thinking) {
    @apply h-0 !text-0 bg-transparent mb-[-32px];

    .juchats-code-wrapper {
      @apply pointer-events-none
    }

    .juchats-code-wrapper * {
      @apply invisible!
    }

    svg {
      transform: rotate(180deg);
    }


    code {
      font-size: 0;
    }

    code:not(.language-thinking) span {
      font-size: 0;
    }

    .code-wrapper {
      display: none;
    }
  }
  
  .juchats-thinking {
    p:first-child {
      @apply mt-0
    }

    p:last-child {
      @apply mb-0;
    }
  }

  pre.hljs:has(.language-thinking) {
    .code-wrapper,
    .code-wrapper span {
      transition: height 0.3s var(--ani-bezier);
      transition-behavior: allow-discrete;
    }

    .code-wrapper {
      position: relative;

      li, p {
        margin: 0;
      }


      li, ol {
        line-height: 16px;
      }

    
    }
  }
}

.hljs {
  transition: all 0.3s var(--ani-bezier);
}

.hljs:has(.render-mermaid) {
  @apply dark:bg-#1C1F24 !pt-41px;
}

.hljs.code-nowrap .code-wrapper {
  overflow-x: auto !important;
  transition: none;
  
  &::-webkit-scrollbar {
    height: 8px;
  }
}

.hljs.code-nowrap .code-wrapper code,
.hljs.code-nowrap .code-wrapper span.inline-block {
  white-space: pre !important;
  word-break: normal !important;
  overflow-x: visible !important;
}

.code-block-collapsed {
  height: 0 !important;
  padding-bottom: 0 !important;
  overflow: hidden !important;
  padding-top: 40px !important;
  transition: all 0.3s var(--ani-bezier);
  
  .code-wrapper {
    &::-webkit-scrollbar {
      display: none;
    }
  }
}

.rotate--90 {
  transform: rotate(-90deg);
}

[aria-roledescription="error"] {
  display: none;
}

@keyframes shark-animation {
  to {
    background-position: 200%;
  }
}

.shark-animation {
  display: inline-block;
  color: var(--shark-text-color, currentColor);
  position: relative;
  -webkit-text-fill-color: transparent;
  background: linear-gradient(
    -45deg,
    rgba(255, 255, 255, 0%) 10%,
    var(--shark-shadow-color, rgba(255, 255, 255, 70%)),
    rgba(255, 255, 255, 0%) 90%
  ) -100% / 50% no-repeat currentcolor;
  background-clip: text;
  -webkit-background-clip: text;
  animation: shark-animation 2s infinite linear;
}

.code-wrapper {
  position: relative;
  width: 100%;
  display: inline-block;
  overflow-wrap: break-word;
  transition: width 0.3s var(--ani-bezier), max-width 0.3s var(--ani-bezier);
  overflow: visible;
}

.citation {
  @apply transition-all duration-100 inline-flex justify-center items-center text-center w-[18px] h-[18px] rounded-full bg-[var(--artifacts-code-border)] hover:bg-black dark:hover:bg-white text-#727272 dark:text-#A8AFBF hover:text-white dark:hover:text-black cursor-pointer text-[10px] font-bold mt-[-0.3em] ml-4px;

  vertical-align: middle;

  &.pdf {
    @apply w-auto px-2;
  }
}


.tippy-box {
  background-color: transparent !important;
}

.tippy-content {
  @apply p-0!;
}
