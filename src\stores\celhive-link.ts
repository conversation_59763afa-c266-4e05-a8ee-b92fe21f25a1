const PluginEventName = 'CelHive-Link-Event'

enum AgentAction {
  QuoteTxt = 'quote-txt',
  ImgPaste = 'img-paste',
  SummaryLink = 'summary-link',
  SelectText = 'select-text',
  TabHTML = 'tab-html',
  CaptureImg = 'capture-img',
  TabList = 'tabs',
}

interface PluginAgentEventProps {
  action: AgentAction
  data: string
}

export const useCelHiveLink = defineStore('celhive-link', () => {
  const IsCelHiveLinkIn = ref(false)
  const tabs = ref([] as chrome.tabs.Tab[])

  const reciveTextFn = new Set<(text: string) => void>()
  const pasteImgFn = new Set<() => void>()
  const summaryLinkFn = new Set<(link: string) => void>()
  const reciveBase64Fn = new Set<(base64: string) => void>()

  const customEvent = function (e: CustomEvent<PluginAgentEventProps>) {
    if (e.detail.action === AgentAction.QuoteTxt) {
      reciveTextFn.forEach(fn => fn(e.detail.data))
    }
    else if (e.detail.action === AgentAction.ImgPaste) {
      pasteImgFn.forEach(fn => fn())
    }
    else if (e.detail.action === AgentAction.SummaryLink) {
      summaryLinkFn.forEach(fn => fn(e.detail.data))
    }
    else if (e.detail.action === AgentAction.SelectText) {
      reciveTextFn.forEach(fn => fn(e.detail.data))
    }
  } as EventListener

  const observerAction = () => {
    IsCelHiveLinkIn.value = document.body.getAttribute('data-celhive-link') === 'true'
  }

  const observer = new MutationObserver(observerAction)

  const onQuoteTxt = (callback: (text: string) => void) => {
    reciveTextFn.add(callback)
  }

  const onPasteImgFn = (callback: () => void) => {
    pasteImgFn.add(callback)
  }

  const onSummaryLinkFn = (callback: (link: string) => void) => {
    summaryLinkFn.add(callback)
  }

  const onReciveBase64 = (callback: (base64: string) => void) => {
    reciveBase64Fn.add(callback)
  }

  const onMessage = (e: MessageEvent<{ action: AgentAction, data: string }>) => {
    if (IsCelHiveLinkIn.value === false) {
      return
    }

    if (e.data.action === AgentAction.SelectText) {
      reciveTextFn.forEach(fn => fn(e.data.data))
    }
    else if (e.data.action === AgentAction.TabHTML) {
      sessionStorage.setItem('celhive-link-html', e.data.data)
    }
    else if (e.data.action === AgentAction.CaptureImg) {
      if (e.data.data.startsWith('data:image/')) {
        reciveBase64Fn.forEach(fn => fn(e.data.data))
      }
    }
    else if (e.data.action === AgentAction.TabList) {
      tabs.value = e.data.data as unknown as chrome.tabs.Tab[]
    }
  }

  onMounted(() => {
    document.addEventListener(PluginEventName, customEvent)
    observer.observe(document.body, {
      attributes: true,
    })
    observerAction()
    window.addEventListener('message', onMessage)
  })

  onUnmounted(() => {
    reciveTextFn.clear()
    pasteImgFn.clear()
    summaryLinkFn.clear()
    observer.disconnect()
    document.removeEventListener(PluginEventName, customEvent)
    window.removeEventListener('message', onMessage)
  })

  return {
    IsCelHiveLinkIn,
    tabs,
    onQuoteTxt,
    onPasteImgFn,
    onSummaryLinkFn,
    onReciveBase64,
  }
})
