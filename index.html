<!DOCTYPE html>
<html lang="zh" translate="no">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" href="/favicon.ico" />
  <link rel="icon" href="/favicon-light.svg" type="image/svg+xml">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://www.hermchats.com/" />
  <meta property="og:title" content="Juchats ｜ Address user needs using natural language" />
  <meta property="og:description" content="Juchats ｜  One Window, Infinite Worlds! ｜LLMs｜RAG｜Agent｜AI Human" />
  <meta property="og:image" content="https://hermosssvip.herm.tools/OGImageNew.png" />
  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:url" content="https://www.hermchats.com/" />
  <meta property="twitter:title" content="Juchats ｜ Address user needs using natural language" />
  <meta property="twitter:description" content="Juchats ｜ One Window, Infinite Worlds! ｜LLMs｜RAG｜Agent｜AI Human" />
  <meta property="twitter:image" content="https://hermosssvip.herm.tools/OGImageNew.png" />
  <meta name="description" content="Juchats ｜ One Window, Infinite Worlds! ｜LLMs｜RAG｜Agent｜AI Human">
  <title>Juchats ｜ One Window, Infinite Worlds!</title>
  <meta name="theme-color" content="transparent">
  <script async src="https://fastly.jsdelivr.net/npm/mermaid@11.4.1/dist/mermaid.min.js"></script>
  <script src="https://fastly.jsdelivr.net/npm/smiles-drawer@2.1.7/dist/smiles-drawer.min.js"></script>
  <!-- Mol* (Mol-star) for protein visualization -->
<script src="https://fastly.jsdelivr.net/npm/molstar@4.18.0/build/viewer/molstar.min.js"></script>
<link href="https://fastly.jsdelivr.net/npm/molstar@4.18.0/build/viewer/molstar.min.css" rel="stylesheet">
  <script>
    window.addEventListener('message', function (event) {
      const fromCelhiveLink = event.data && event.data.message && event.data.message === 'CelHive Link';
      if (fromCelhiveLink) {
        document.body.setAttribute('data-celhive-link', 'true');
        window.removeEventListener('message', arguments.callee);
      }
    });
  </script>
</head>

<body>
  <div id="app" style="height: 100%;"></div>

  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', function () {
        navigator.serviceWorker.register('/sw.js').then(function (registration) {
        }, function (err) {
          console.log('Service Worker fail:', err);
        });
      });
    }

    const isDark = () => {
      const theme = document.documentElement.getAttribute('class');
      const dark = theme && theme.includes('dark')
      return dark
    }

    const setStatusbarTheme = () => {
      const dark = isDark()
      document.querySelector('meta[name="theme-color"]').setAttribute('content', dark ? '#000000' : '#ffffff');
    }

    const observer = new MutationObserver((mutationsList) => {
      for (let mutation of mutationsList) {
        if (mutation.type === 'attributes') {
          if (mutation.attributeName === 'class') {
            setStatusbarTheme()
          }
        }
      }
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class'],
    });

    setStatusbarTheme()
  </script>

  <script type="module" src="/src/main.ts"></script>
  <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script> -->


  <!-- Crisp -->
  <!-- <script type="text/javascript">
      window.$crisp = []
      window.CRISP_WEBSITE_ID = '793e35fb-dd14-4153-bd92-15b32d5c36c6'
      ;(function () {
        d = document
        s = d.createElement('script')
        s.src = 'https://client.crisp.chat/l.js'
        s.async = 1
        d.getElementsByTagName('head')[0].appendChild(s)
      })()
    </script> -->

  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-BGPCRVYLM7"></script>
  <script>
    window.dataLayer = window.dataLayer || []
    function gtag() {
      dataLayer.push(arguments)
    }
    gtag('js', new Date())

    gtag('config', 'G-BGPCRVYLM7')
  </script>
</body>

</html>
